/* Licence
 * Company: MCUSTUDIO
 * Auther: Ahypnis.
 * Version: V0.10
 * Time: 2025/06/05
 * Note:
 */

#include "mcu_cmic_gd32f470vet6.h"

extern uint16_t adc_value[1];
extern rtc_parameter_struct rtc_initpara;

// System startup flag
static uint8_t system_startup_complete = 0;
static uint32_t startup_display_time = 0;

/**
 * @brief	ʹ������printf�ķ�ʽ��ʾ�ַ�������ʾ6x8��С��ASCII�ַ�
 * @param x  Character position on the X-axis  range��0 - 127
 * @param y  Character position on the Y-axis  range��0 - 3
 * ���磺oled_printf(0, 0, "Data = %d", dat);
 **/
int oled_printf(uint8_t x, uint8_t y, const char *format, ...)
{
  char buffer[512]; // ��ʱ�洢��ʽ������ַ���
  va_list arg;      // �����ɱ����
  int len;          // �����ַ�������

  va_start(arg, format);
  // ��ȫ�ظ�ʽ���ַ����� buffer
  len = vsnprintf(buffer, sizeof(buffer), format, arg);
  va_end(arg);

  OLED_ShowStr(x, y, buffer, 8);
  return len;
}

void oled_task(void)
{
  data_logger_t *logger_data = get_data_logger_state();

  // Handle startup display
  if (!system_startup_complete)
  {
    if (startup_display_time == 0)
    {
      startup_display_time = get_system_ms();
      // Clear screen and show welcome message
      OLED_Clear();
      oled_printf(0, 0, "Data Logger");
      oled_printf(0, 1, "Name: ZhiBo Lin"); // Replace with your name
      oled_printf(0, 2, "ID:23141050312");  // Replace with your student ID
      oled_printf(0, 3, "Loading...");

      // Try to read data from flash on startup
      data_logger_read_from_flash();
    }

    // Show startup screen for 3 seconds
    if (get_system_ms() - startup_display_time > 3000)
    {
      system_startup_complete = 1;
      OLED_Clear();
    }
    return;
  }

  // Normal operation display
  // Line 0: Current time (real-time clock)
  rtc_current_time_get(&rtc_initpara);
  oled_printf(0, 0, "20%02x-%02x-%02x %02x:%02x:%02x",
              rtc_initpara.year, rtc_initpara.month, rtc_initpara.date,
              rtc_initpara.hour, rtc_initpara.minute, rtc_initpara.second);

  // Line 1: Recording status
  oled_printf(0, 1, "Status: %s",
              logger_data->recording_state ? "Recording" : "Stopped");

  // Line 2: Empty line for better layout
  oled_printf(0, 2, "");

  // Line 3: All button press counts
  oled_printf(0, 3, "K1:%ld K2:%ld K3:%ld K4:%ld",
              logger_data->key1_count, logger_data->key2_count,
              logger_data->key3_count, logger_data->key4_count);
}

/* CUSTOM EDIT */

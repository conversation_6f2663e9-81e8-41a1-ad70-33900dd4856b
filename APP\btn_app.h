#ifndef __BTN_APP_H
#define __BTN_APP_H

#include "stdint.h"

#ifdef __cplusplus
extern "C"
{
#endif

    // Data Logger structure
    typedef struct
    {
        uint32_t key1_count;     // Start/Stop button count
        uint32_t key2_count;     // Clear button count
        uint32_t key3_count;     // Save button count
        uint32_t key4_count;     // Read button count
        uint8_t recording_state; // 0: stopped, 1: recording
    } data_logger_t;

    void app_btn_init(void);
    void btn_task(void);

    // Data Logger functions
    void data_logger_save_to_flash(void);
    void data_logger_read_from_flash(void);
    void data_logger_clear_current(void);
    void data_logger_toggle_recording(void);
    data_logger_t *get_data_logger_state(void);

#ifdef __cplusplus
}
#endif

#endif /* __BTN_APP_H */

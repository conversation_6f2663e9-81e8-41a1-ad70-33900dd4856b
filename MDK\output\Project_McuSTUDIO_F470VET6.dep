Dependencies for Project 'Project', Target 'McuSTUDIO_F470VET6': (DO NOT MODIFY !)
CompilerVersion: 6220000::V6.22::ARMCLANG
F (..\USER\src\gd32f4xx_it.c)(0x6846EBAD)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O1 -ffunction-sections -w -I ../Libraries/Include -I ../USER/inc -I ../Driver/CMSIS/GD/GD32F4xx/Include -I ../Components/bsp -I ../Components/oled -I ../Components/gd25qxx -I ../Components/ebtn -I ../Components/sdio -I ../Components/fatfs -I ../APP

-I./RTE/_McuSTUDIO_F470VET6

-IC:/Keil_v5/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-IC:/Keil_v5/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-IC:/Keil_v5/GorgonMeducer/perf_counter/2.3.3

-D__UVISION_VERSION="541" -DGD32F470 -D_RTE_ -DUSE_STDPERIPH_DRIVER -DGD32F470  -include "Pre_Include_Global.h"

-o ./output/gd32f4xx_it.o -MMD)
I (RTE\_McuSTUDIO_F470VET6\Pre_Include_Global.h)(0x6846EBF3)
I (..\USER\inc\gd32f4xx_it.h)(0x6846EBAD)
I (..\Driver\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x6846EBA6)
I (C:\Keil_v5\ARM\CMSIS\6.1.0\CMSIS\Core\Include\core_cm4.h)(0x664BD888)
I (..\Driver\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x6846EBA6)
I (..\USER\inc\gd32f4xx_libopt.h)(0x6846EBAD)
I (..\Libraries\Include\gd32f4xx_rcu.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_adc.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_can.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_crc.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_ctc.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_dac.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_dbg.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_dci.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_dma.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_exti.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_fmc.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_fwdgt.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_gpio.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_syscfg.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_i2c.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_iref.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_pmu.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_rtc.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_sdio.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_spi.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_timer.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_trng.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_usart.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_wwdgt.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_misc.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_enet.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_exmc.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_ipa.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_tli.h)(0x6846EBA7)
I (..\USER\inc\main.h)(0x6846EBAD)
I (..\USER\inc\systick.h)(0x6846EBAD)
I (..\Components\sdio\sdio_sdcard.h)(0x6846EBA6)
F (..\USER\src\main.c)(0x6846EEAC)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O1 -ffunction-sections -w -I ../Libraries/Include -I ../USER/inc -I ../Driver/CMSIS/GD/GD32F4xx/Include -I ../Components/bsp -I ../Components/oled -I ../Components/gd25qxx -I ../Components/ebtn -I ../Components/sdio -I ../Components/fatfs -I ../APP

-I./RTE/_McuSTUDIO_F470VET6

-IC:/Keil_v5/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-IC:/Keil_v5/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-IC:/Keil_v5/GorgonMeducer/perf_counter/2.3.3

-D__UVISION_VERSION="541" -DGD32F470 -D_RTE_ -DUSE_STDPERIPH_DRIVER -DGD32F470  -include "Pre_Include_Global.h"

-o ./output/main.o -MMD)
I (RTE\_McuSTUDIO_F470VET6\Pre_Include_Global.h)(0x6846EBF3)
I (..\Components\bsp\mcu_cmic_gd32f470vet6.h)(0x6846EBA5)
I (..\Driver\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x6846EBA6)
I (C:\Keil_v5\ARM\CMSIS\6.1.0\CMSIS\Core\Include\core_cm4.h)(0x664BD888)
I (..\Driver\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x6846EBA6)
I (..\USER\inc\gd32f4xx_libopt.h)(0x6846EBAD)
I (..\Libraries\Include\gd32f4xx_rcu.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_adc.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_can.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_crc.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_ctc.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_dac.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_dbg.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_dci.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_dma.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_exti.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_fmc.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_fwdgt.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_gpio.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_syscfg.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_i2c.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_iref.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_pmu.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_rtc.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_sdio.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_spi.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_timer.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_trng.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_usart.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_wwdgt.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_misc.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_enet.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_exmc.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_ipa.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_tli.h)(0x6846EBA7)
I (..\USER\inc\systick.h)(0x6846EBAD)
I (..\Components\ebtn\ebtn.h)(0x6846EBA5)
I (..\Components\ebtn\bit_array.h)(0x6846EBA5)
I (..\Components\oled\oled.h)(0x6846EBA6)
I (..\Components\gd25qxx\gd25qxx.h)(0x6846EBA6)
I (..\Components\sdio\sdio_sdcard.h)(0x6846EBA6)
I (..\Components\fatfs\ff.h)(0x6846EBA5)
I (..\Components\fatfs\integer.h)(0x6846EBA5)
I (..\Components\fatfs\ffconf.h)(0x6846EBA5)
I (..\Components\fatfs\diskio.h)(0x6846EBA5)
I (..\APP\sd_app.h)(0x6846EBA5)
I (..\APP\led_app.h)(0x6846EBA5)
I (..\APP\adc_app.h)(0x6846EBA5)
I (..\APP\oled_app.h)(0x6846EBA5)
I (..\APP\usart_app.h)(0x6846EBA5)
I (..\APP\rtc_app.h)(0x6846EBA5)
I (..\APP\btn_app.h)(0x6846EDF1)
I (..\APP\scheduler.h)(0x6846EBA5)
I (C:\Keil_v5\GorgonMeducer\perf_counter\2.3.3\perf_counter.h)(0x6842F7C4)
I (C:\Keil_v5\GorgonMeducer\perf_counter\2.3.3\perfc_port_default.h)(0x6842F7C4)
I (C:\Keil_v5\ARM\CMSIS\6.1.0\CMSIS\Core\Include\cmsis_compiler.h)(0x664BD888)
F (..\USER\src\systick.c)(0x6846EBAD)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O1 -ffunction-sections -w -I ../Libraries/Include -I ../USER/inc -I ../Driver/CMSIS/GD/GD32F4xx/Include -I ../Components/bsp -I ../Components/oled -I ../Components/gd25qxx -I ../Components/ebtn -I ../Components/sdio -I ../Components/fatfs -I ../APP

-I./RTE/_McuSTUDIO_F470VET6

-IC:/Keil_v5/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-IC:/Keil_v5/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-IC:/Keil_v5/GorgonMeducer/perf_counter/2.3.3

-D__UVISION_VERSION="541" -DGD32F470 -D_RTE_ -DUSE_STDPERIPH_DRIVER -DGD32F470  -include "Pre_Include_Global.h"

-o ./output/systick.o -MMD)
I (RTE\_McuSTUDIO_F470VET6\Pre_Include_Global.h)(0x6846EBF3)
I (..\Driver\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x6846EBA6)
I (C:\Keil_v5\ARM\CMSIS\6.1.0\CMSIS\Core\Include\core_cm4.h)(0x664BD888)
I (..\Driver\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x6846EBA6)
I (..\USER\inc\gd32f4xx_libopt.h)(0x6846EBAD)
I (..\Libraries\Include\gd32f4xx_rcu.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_adc.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_can.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_crc.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_ctc.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_dac.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_dbg.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_dci.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_dma.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_exti.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_fmc.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_fwdgt.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_gpio.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_syscfg.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_i2c.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_iref.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_pmu.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_rtc.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_sdio.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_spi.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_timer.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_trng.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_usart.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_wwdgt.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_misc.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_enet.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_exmc.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_ipa.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_tli.h)(0x6846EBA7)
I (..\USER\inc\systick.h)(0x6846EBAD)
F (..\Components\bsp\mcu_cmic_gd32f470vet6.c)(0x6846EBA5)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O1 -ffunction-sections -w -I ../Libraries/Include -I ../USER/inc -I ../Driver/CMSIS/GD/GD32F4xx/Include -I ../Components/bsp -I ../Components/oled -I ../Components/gd25qxx -I ../Components/ebtn -I ../Components/sdio -I ../Components/fatfs -I ../APP

-I./RTE/_McuSTUDIO_F470VET6

-IC:/Keil_v5/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-IC:/Keil_v5/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-IC:/Keil_v5/GorgonMeducer/perf_counter/2.3.3

-D__UVISION_VERSION="541" -DGD32F470 -D_RTE_ -DUSE_STDPERIPH_DRIVER -DGD32F470  -include "Pre_Include_Global.h"

-o ./output/mcu_cmic_gd32f470vet6.o -MMD)
I (RTE\_McuSTUDIO_F470VET6\Pre_Include_Global.h)(0x6846EBF3)
I (..\Components\bsp\mcu_cmic_gd32f470vet6.h)(0x6846EBA5)
I (..\Driver\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x6846EBA6)
I (C:\Keil_v5\ARM\CMSIS\6.1.0\CMSIS\Core\Include\core_cm4.h)(0x664BD888)
I (..\Driver\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x6846EBA6)
I (..\USER\inc\gd32f4xx_libopt.h)(0x6846EBAD)
I (..\Libraries\Include\gd32f4xx_rcu.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_adc.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_can.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_crc.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_ctc.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_dac.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_dbg.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_dci.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_dma.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_exti.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_fmc.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_fwdgt.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_gpio.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_syscfg.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_i2c.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_iref.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_pmu.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_rtc.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_sdio.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_spi.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_timer.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_trng.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_usart.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_wwdgt.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_misc.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_enet.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_exmc.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_ipa.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_tli.h)(0x6846EBA7)
I (..\USER\inc\systick.h)(0x6846EBAD)
I (..\Components\ebtn\ebtn.h)(0x6846EBA5)
I (..\Components\ebtn\bit_array.h)(0x6846EBA5)
I (..\Components\oled\oled.h)(0x6846EBA6)
I (..\Components\gd25qxx\gd25qxx.h)(0x6846EBA6)
I (..\Components\sdio\sdio_sdcard.h)(0x6846EBA6)
I (..\Components\fatfs\ff.h)(0x6846EBA5)
I (..\Components\fatfs\integer.h)(0x6846EBA5)
I (..\Components\fatfs\ffconf.h)(0x6846EBA5)
I (..\Components\fatfs\diskio.h)(0x6846EBA5)
I (..\APP\sd_app.h)(0x6846EBA5)
I (..\APP\led_app.h)(0x6846EBA5)
I (..\APP\adc_app.h)(0x6846EBA5)
I (..\APP\oled_app.h)(0x6846EBA5)
I (..\APP\usart_app.h)(0x6846EBA5)
I (..\APP\rtc_app.h)(0x6846EBA5)
I (..\APP\btn_app.h)(0x6846EDF1)
I (..\APP\scheduler.h)(0x6846EBA5)
I (C:\Keil_v5\GorgonMeducer\perf_counter\2.3.3\perf_counter.h)(0x6842F7C4)
I (C:\Keil_v5\GorgonMeducer\perf_counter\2.3.3\perfc_port_default.h)(0x6842F7C4)
I (C:\Keil_v5\ARM\CMSIS\6.1.0\CMSIS\Core\Include\cmsis_compiler.h)(0x664BD888)
F (..\Components\bsp\mcu_cmic_gd32f470vet6.h)(0x6846EBA5)()
F (..\Components\gd25qxx\gd25qxx.c)(0x6846EBA6)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O1 -ffunction-sections -w -I ../Libraries/Include -I ../USER/inc -I ../Driver/CMSIS/GD/GD32F4xx/Include -I ../Components/bsp -I ../Components/oled -I ../Components/gd25qxx -I ../Components/ebtn -I ../Components/sdio -I ../Components/fatfs -I ../APP

-I./RTE/_McuSTUDIO_F470VET6

-IC:/Keil_v5/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-IC:/Keil_v5/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-IC:/Keil_v5/GorgonMeducer/perf_counter/2.3.3

-D__UVISION_VERSION="541" -DGD32F470 -D_RTE_ -DUSE_STDPERIPH_DRIVER -DGD32F470  -include "Pre_Include_Global.h"

-o ./output/gd25qxx.o -MMD)
I (RTE\_McuSTUDIO_F470VET6\Pre_Include_Global.h)(0x6846EBF3)
I (..\Components\bsp\mcu_cmic_gd32f470vet6.h)(0x6846EBA5)
I (..\Driver\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x6846EBA6)
I (C:\Keil_v5\ARM\CMSIS\6.1.0\CMSIS\Core\Include\core_cm4.h)(0x664BD888)
I (..\Driver\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x6846EBA6)
I (..\USER\inc\gd32f4xx_libopt.h)(0x6846EBAD)
I (..\Libraries\Include\gd32f4xx_rcu.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_adc.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_can.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_crc.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_ctc.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_dac.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_dbg.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_dci.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_dma.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_exti.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_fmc.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_fwdgt.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_gpio.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_syscfg.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_i2c.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_iref.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_pmu.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_rtc.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_sdio.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_spi.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_timer.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_trng.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_usart.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_wwdgt.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_misc.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_enet.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_exmc.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_ipa.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_tli.h)(0x6846EBA7)
I (..\USER\inc\systick.h)(0x6846EBAD)
I (..\Components\ebtn\ebtn.h)(0x6846EBA5)
I (..\Components\ebtn\bit_array.h)(0x6846EBA5)
I (..\Components\oled\oled.h)(0x6846EBA6)
I (..\Components\gd25qxx\gd25qxx.h)(0x6846EBA6)
I (..\Components\sdio\sdio_sdcard.h)(0x6846EBA6)
I (..\Components\fatfs\ff.h)(0x6846EBA5)
I (..\Components\fatfs\integer.h)(0x6846EBA5)
I (..\Components\fatfs\ffconf.h)(0x6846EBA5)
I (..\Components\fatfs\diskio.h)(0x6846EBA5)
I (..\APP\sd_app.h)(0x6846EBA5)
I (..\APP\led_app.h)(0x6846EBA5)
I (..\APP\adc_app.h)(0x6846EBA5)
I (..\APP\oled_app.h)(0x6846EBA5)
I (..\APP\usart_app.h)(0x6846EBA5)
I (..\APP\rtc_app.h)(0x6846EBA5)
I (..\APP\btn_app.h)(0x6846EDF1)
I (..\APP\scheduler.h)(0x6846EBA5)
I (C:\Keil_v5\GorgonMeducer\perf_counter\2.3.3\perf_counter.h)(0x6842F7C4)
I (C:\Keil_v5\GorgonMeducer\perf_counter\2.3.3\perfc_port_default.h)(0x6842F7C4)
I (C:\Keil_v5\ARM\CMSIS\6.1.0\CMSIS\Core\Include\cmsis_compiler.h)(0x664BD888)
F (..\Components\gd25qxx\lfs.c)(0x6846EBA6)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O1 -ffunction-sections -w -I ../Libraries/Include -I ../USER/inc -I ../Driver/CMSIS/GD/GD32F4xx/Include -I ../Components/bsp -I ../Components/oled -I ../Components/gd25qxx -I ../Components/ebtn -I ../Components/sdio -I ../Components/fatfs -I ../APP

-I./RTE/_McuSTUDIO_F470VET6

-IC:/Keil_v5/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-IC:/Keil_v5/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-IC:/Keil_v5/GorgonMeducer/perf_counter/2.3.3

-D__UVISION_VERSION="541" -DGD32F470 -D_RTE_ -DUSE_STDPERIPH_DRIVER -DGD32F470  -include "Pre_Include_Global.h"

-o ./output/lfs.o -MMD)
I (RTE\_McuSTUDIO_F470VET6\Pre_Include_Global.h)(0x6846EBF3)
I (..\Components\gd25qxx\lfs.h)(0x6846EBA6)
I (..\Components\gd25qxx\lfs_util.h)(0x6846EBA6)
F (..\Components\gd25qxx\lfs_port.c)(0x6846EBA6)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O1 -ffunction-sections -w -I ../Libraries/Include -I ../USER/inc -I ../Driver/CMSIS/GD/GD32F4xx/Include -I ../Components/bsp -I ../Components/oled -I ../Components/gd25qxx -I ../Components/ebtn -I ../Components/sdio -I ../Components/fatfs -I ../APP

-I./RTE/_McuSTUDIO_F470VET6

-IC:/Keil_v5/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-IC:/Keil_v5/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-IC:/Keil_v5/GorgonMeducer/perf_counter/2.3.3

-D__UVISION_VERSION="541" -DGD32F470 -D_RTE_ -DUSE_STDPERIPH_DRIVER -DGD32F470  -include "Pre_Include_Global.h"

-o ./output/lfs_port.o -MMD)
I (RTE\_McuSTUDIO_F470VET6\Pre_Include_Global.h)(0x6846EBF3)
I (..\Components\gd25qxx\lfs_port.h)(0x6846EBA6)
I (..\Components\gd25qxx\lfs.h)(0x6846EBA6)
I (..\Components\gd25qxx\gd25qxx.h)(0x6846EBA6)
I (..\Driver\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x6846EBA6)
I (C:\Keil_v5\ARM\CMSIS\6.1.0\CMSIS\Core\Include\core_cm4.h)(0x664BD888)
I (..\Driver\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x6846EBA6)
I (..\USER\inc\gd32f4xx_libopt.h)(0x6846EBAD)
I (..\Libraries\Include\gd32f4xx_rcu.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_adc.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_can.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_crc.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_ctc.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_dac.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_dbg.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_dci.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_dma.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_exti.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_fmc.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_fwdgt.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_gpio.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_syscfg.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_i2c.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_iref.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_pmu.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_rtc.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_sdio.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_spi.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_timer.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_trng.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_usart.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_wwdgt.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_misc.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_enet.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_exmc.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_ipa.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_tli.h)(0x6846EBA7)
F (..\Components\gd25qxx\lfs_util.c)(0x6846EBA6)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O1 -ffunction-sections -w -I ../Libraries/Include -I ../USER/inc -I ../Driver/CMSIS/GD/GD32F4xx/Include -I ../Components/bsp -I ../Components/oled -I ../Components/gd25qxx -I ../Components/ebtn -I ../Components/sdio -I ../Components/fatfs -I ../APP

-I./RTE/_McuSTUDIO_F470VET6

-IC:/Keil_v5/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-IC:/Keil_v5/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-IC:/Keil_v5/GorgonMeducer/perf_counter/2.3.3

-D__UVISION_VERSION="541" -DGD32F470 -D_RTE_ -DUSE_STDPERIPH_DRIVER -DGD32F470  -include "Pre_Include_Global.h"

-o ./output/lfs_util.o -MMD)
I (RTE\_McuSTUDIO_F470VET6\Pre_Include_Global.h)(0x6846EBF3)
I (..\Components\gd25qxx\lfs_util.h)(0x6846EBA6)
F (..\Components\oled\oled.c)(0x6846EBA6)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O1 -ffunction-sections -w -I ../Libraries/Include -I ../USER/inc -I ../Driver/CMSIS/GD/GD32F4xx/Include -I ../Components/bsp -I ../Components/oled -I ../Components/gd25qxx -I ../Components/ebtn -I ../Components/sdio -I ../Components/fatfs -I ../APP

-I./RTE/_McuSTUDIO_F470VET6

-IC:/Keil_v5/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-IC:/Keil_v5/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-IC:/Keil_v5/GorgonMeducer/perf_counter/2.3.3

-D__UVISION_VERSION="541" -DGD32F470 -D_RTE_ -DUSE_STDPERIPH_DRIVER -DGD32F470  -include "Pre_Include_Global.h"

-o ./output/oled.o -MMD)
I (RTE\_McuSTUDIO_F470VET6\Pre_Include_Global.h)(0x6846EBF3)
I (..\Components\oled\oled.h)(0x6846EBA6)
I (..\Components\oled\oledfont.h)(0x6846EBA6)
I (..\Libraries\Include\gd32f4xx_i2c.h)(0x6846EBA7)
I (..\Driver\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x6846EBA6)
I (C:\Keil_v5\ARM\CMSIS\6.1.0\CMSIS\Core\Include\core_cm4.h)(0x664BD888)
I (..\Driver\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x6846EBA6)
I (..\USER\inc\gd32f4xx_libopt.h)(0x6846EBAD)
I (..\Libraries\Include\gd32f4xx_rcu.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_adc.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_can.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_crc.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_ctc.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_dac.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_dbg.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_dci.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_dma.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_exti.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_fmc.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_fwdgt.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_gpio.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_syscfg.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_iref.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_pmu.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_rtc.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_sdio.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_spi.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_timer.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_trng.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_usart.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_wwdgt.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_misc.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_enet.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_exmc.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_ipa.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_tli.h)(0x6846EBA7)
I (C:\Keil_v5\GorgonMeducer\perf_counter\2.3.3\perf_counter.h)(0x6842F7C4)
I (C:\Keil_v5\GorgonMeducer\perf_counter\2.3.3\perfc_port_default.h)(0x6842F7C4)
I (C:\Keil_v5\ARM\CMSIS\6.1.0\CMSIS\Core\Include\cmsis_compiler.h)(0x664BD888)
F (..\Components\ebtn\bit_array.h)(0x6846EBA5)()
F (..\Components\ebtn\ebtn.c)(0x6846EBA5)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O1 -ffunction-sections -w -I ../Libraries/Include -I ../USER/inc -I ../Driver/CMSIS/GD/GD32F4xx/Include -I ../Components/bsp -I ../Components/oled -I ../Components/gd25qxx -I ../Components/ebtn -I ../Components/sdio -I ../Components/fatfs -I ../APP

-I./RTE/_McuSTUDIO_F470VET6

-IC:/Keil_v5/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-IC:/Keil_v5/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-IC:/Keil_v5/GorgonMeducer/perf_counter/2.3.3

-D__UVISION_VERSION="541" -DGD32F470 -D_RTE_ -DUSE_STDPERIPH_DRIVER -DGD32F470  -include "Pre_Include_Global.h"

-o ./output/ebtn.o -MMD)
I (RTE\_McuSTUDIO_F470VET6\Pre_Include_Global.h)(0x6846EBF3)
I (..\Components\ebtn\ebtn.h)(0x6846EBA5)
I (..\Components\ebtn\bit_array.h)(0x6846EBA5)
F (..\Components\ebtn\ebtn.h)(0x6846EBA5)()
F (..\Components\sdio\sdio_sdcard.c)(0x6846EBA6)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O1 -ffunction-sections -w -I ../Libraries/Include -I ../USER/inc -I ../Driver/CMSIS/GD/GD32F4xx/Include -I ../Components/bsp -I ../Components/oled -I ../Components/gd25qxx -I ../Components/ebtn -I ../Components/sdio -I ../Components/fatfs -I ../APP

-I./RTE/_McuSTUDIO_F470VET6

-IC:/Keil_v5/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-IC:/Keil_v5/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-IC:/Keil_v5/GorgonMeducer/perf_counter/2.3.3

-D__UVISION_VERSION="541" -DGD32F470 -D_RTE_ -DUSE_STDPERIPH_DRIVER -DGD32F470  -include "Pre_Include_Global.h"

-o ./output/sdio_sdcard.o -MMD)
I (RTE\_McuSTUDIO_F470VET6\Pre_Include_Global.h)(0x6846EBF3)
I (..\Components\sdio\sdio_sdcard.h)(0x6846EBA6)
I (..\Driver\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x6846EBA6)
I (C:\Keil_v5\ARM\CMSIS\6.1.0\CMSIS\Core\Include\core_cm4.h)(0x664BD888)
I (..\Driver\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x6846EBA6)
I (..\USER\inc\gd32f4xx_libopt.h)(0x6846EBAD)
I (..\Libraries\Include\gd32f4xx_rcu.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_adc.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_can.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_crc.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_ctc.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_dac.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_dbg.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_dci.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_dma.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_exti.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_fmc.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_fwdgt.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_gpio.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_syscfg.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_i2c.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_iref.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_pmu.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_rtc.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_sdio.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_spi.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_timer.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_trng.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_usart.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_wwdgt.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_misc.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_enet.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_exmc.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_ipa.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_tli.h)(0x6846EBA7)
F (..\Components\fatfs\ff.c)(0x6846EBA5)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O1 -ffunction-sections -w -I ../Libraries/Include -I ../USER/inc -I ../Driver/CMSIS/GD/GD32F4xx/Include -I ../Components/bsp -I ../Components/oled -I ../Components/gd25qxx -I ../Components/ebtn -I ../Components/sdio -I ../Components/fatfs -I ../APP

-I./RTE/_McuSTUDIO_F470VET6

-IC:/Keil_v5/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-IC:/Keil_v5/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-IC:/Keil_v5/GorgonMeducer/perf_counter/2.3.3

-D__UVISION_VERSION="541" -DGD32F470 -D_RTE_ -DUSE_STDPERIPH_DRIVER -DGD32F470  -include "Pre_Include_Global.h"

-o ./output/ff.o -MMD)
I (RTE\_McuSTUDIO_F470VET6\Pre_Include_Global.h)(0x6846EBF3)
I (..\Components\fatfs\ff.h)(0x6846EBA5)
I (..\Components\fatfs\integer.h)(0x6846EBA5)
I (..\Components\fatfs\ffconf.h)(0x6846EBA5)
I (..\Components\fatfs\diskio.h)(0x6846EBA5)
F (..\Components\fatfs\diskio.c)(0x6846EBA5)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O1 -ffunction-sections -w -I ../Libraries/Include -I ../USER/inc -I ../Driver/CMSIS/GD/GD32F4xx/Include -I ../Components/bsp -I ../Components/oled -I ../Components/gd25qxx -I ../Components/ebtn -I ../Components/sdio -I ../Components/fatfs -I ../APP

-I./RTE/_McuSTUDIO_F470VET6

-IC:/Keil_v5/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-IC:/Keil_v5/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-IC:/Keil_v5/GorgonMeducer/perf_counter/2.3.3

-D__UVISION_VERSION="541" -DGD32F470 -D_RTE_ -DUSE_STDPERIPH_DRIVER -DGD32F470  -include "Pre_Include_Global.h"

-o ./output/diskio.o -MMD)
I (RTE\_McuSTUDIO_F470VET6\Pre_Include_Global.h)(0x6846EBF3)
I (..\Components\fatfs\diskio.h)(0x6846EBA5)
I (..\Components\fatfs\integer.h)(0x6846EBA5)
I (..\Components\sdio\sdio_sdcard.h)(0x6846EBA6)
I (..\Driver\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x6846EBA6)
I (C:\Keil_v5\ARM\CMSIS\6.1.0\CMSIS\Core\Include\core_cm4.h)(0x664BD888)
I (..\Driver\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x6846EBA6)
I (..\USER\inc\gd32f4xx_libopt.h)(0x6846EBAD)
I (..\Libraries\Include\gd32f4xx_rcu.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_adc.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_can.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_crc.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_ctc.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_dac.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_dbg.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_dci.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_dma.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_exti.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_fmc.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_fwdgt.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_gpio.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_syscfg.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_i2c.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_iref.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_pmu.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_rtc.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_sdio.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_spi.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_timer.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_trng.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_usart.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_wwdgt.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_misc.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_enet.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_exmc.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_ipa.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_tli.h)(0x6846EBA7)
F (..\APP\btn_app.c)(0x6846F05E)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O1 -ffunction-sections -w -I ../Libraries/Include -I ../USER/inc -I ../Driver/CMSIS/GD/GD32F4xx/Include -I ../Components/bsp -I ../Components/oled -I ../Components/gd25qxx -I ../Components/ebtn -I ../Components/sdio -I ../Components/fatfs -I ../APP

-I./RTE/_McuSTUDIO_F470VET6

-IC:/Keil_v5/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-IC:/Keil_v5/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-IC:/Keil_v5/GorgonMeducer/perf_counter/2.3.3

-D__UVISION_VERSION="541" -DGD32F470 -D_RTE_ -DUSE_STDPERIPH_DRIVER -DGD32F470  -include "Pre_Include_Global.h"

-o ./output/btn_app.o -MMD)
I (RTE\_McuSTUDIO_F470VET6\Pre_Include_Global.h)(0x6846EBF3)
I (..\Components\bsp\mcu_cmic_gd32f470vet6.h)(0x6846EBA5)
I (..\Driver\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x6846EBA6)
I (C:\Keil_v5\ARM\CMSIS\6.1.0\CMSIS\Core\Include\core_cm4.h)(0x664BD888)
I (..\Driver\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x6846EBA6)
I (..\USER\inc\gd32f4xx_libopt.h)(0x6846EBAD)
I (..\Libraries\Include\gd32f4xx_rcu.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_adc.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_can.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_crc.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_ctc.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_dac.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_dbg.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_dci.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_dma.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_exti.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_fmc.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_fwdgt.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_gpio.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_syscfg.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_i2c.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_iref.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_pmu.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_rtc.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_sdio.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_spi.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_timer.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_trng.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_usart.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_wwdgt.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_misc.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_enet.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_exmc.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_ipa.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_tli.h)(0x6846EBA7)
I (..\USER\inc\systick.h)(0x6846EBAD)
I (..\Components\ebtn\ebtn.h)(0x6846EBA5)
I (..\Components\ebtn\bit_array.h)(0x6846EBA5)
I (..\Components\oled\oled.h)(0x6846EBA6)
I (..\Components\gd25qxx\gd25qxx.h)(0x6846EBA6)
I (..\Components\sdio\sdio_sdcard.h)(0x6846EBA6)
I (..\Components\fatfs\ff.h)(0x6846EBA5)
I (..\Components\fatfs\integer.h)(0x6846EBA5)
I (..\Components\fatfs\ffconf.h)(0x6846EBA5)
I (..\Components\fatfs\diskio.h)(0x6846EBA5)
I (..\APP\sd_app.h)(0x6846EBA5)
I (..\APP\led_app.h)(0x6846EBA5)
I (..\APP\adc_app.h)(0x6846EBA5)
I (..\APP\oled_app.h)(0x6846EBA5)
I (..\APP\usart_app.h)(0x6846EBA5)
I (..\APP\rtc_app.h)(0x6846EBA5)
I (..\APP\btn_app.h)(0x6846EDF1)
I (..\APP\scheduler.h)(0x6846EBA5)
I (C:\Keil_v5\GorgonMeducer\perf_counter\2.3.3\perf_counter.h)(0x6842F7C4)
I (C:\Keil_v5\GorgonMeducer\perf_counter\2.3.3\perfc_port_default.h)(0x6842F7C4)
I (C:\Keil_v5\ARM\CMSIS\6.1.0\CMSIS\Core\Include\cmsis_compiler.h)(0x664BD888)
F (..\APP\led_app.c)(0x6846F448)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O1 -ffunction-sections -w -I ../Libraries/Include -I ../USER/inc -I ../Driver/CMSIS/GD/GD32F4xx/Include -I ../Components/bsp -I ../Components/oled -I ../Components/gd25qxx -I ../Components/ebtn -I ../Components/sdio -I ../Components/fatfs -I ../APP

-I./RTE/_McuSTUDIO_F470VET6

-IC:/Keil_v5/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-IC:/Keil_v5/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-IC:/Keil_v5/GorgonMeducer/perf_counter/2.3.3

-D__UVISION_VERSION="541" -DGD32F470 -D_RTE_ -DUSE_STDPERIPH_DRIVER -DGD32F470  -include "Pre_Include_Global.h"

-o ./output/led_app.o -MMD)
I (RTE\_McuSTUDIO_F470VET6\Pre_Include_Global.h)(0x6846EBF3)
I (..\Components\bsp\mcu_cmic_gd32f470vet6.h)(0x6846EBA5)
I (..\Driver\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x6846EBA6)
I (C:\Keil_v5\ARM\CMSIS\6.1.0\CMSIS\Core\Include\core_cm4.h)(0x664BD888)
I (..\Driver\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x6846EBA6)
I (..\USER\inc\gd32f4xx_libopt.h)(0x6846EBAD)
I (..\Libraries\Include\gd32f4xx_rcu.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_adc.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_can.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_crc.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_ctc.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_dac.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_dbg.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_dci.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_dma.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_exti.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_fmc.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_fwdgt.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_gpio.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_syscfg.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_i2c.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_iref.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_pmu.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_rtc.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_sdio.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_spi.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_timer.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_trng.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_usart.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_wwdgt.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_misc.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_enet.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_exmc.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_ipa.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_tli.h)(0x6846EBA7)
I (..\USER\inc\systick.h)(0x6846EBAD)
I (..\Components\ebtn\ebtn.h)(0x6846EBA5)
I (..\Components\ebtn\bit_array.h)(0x6846EBA5)
I (..\Components\oled\oled.h)(0x6846EBA6)
I (..\Components\gd25qxx\gd25qxx.h)(0x6846EBA6)
I (..\Components\sdio\sdio_sdcard.h)(0x6846EBA6)
I (..\Components\fatfs\ff.h)(0x6846EBA5)
I (..\Components\fatfs\integer.h)(0x6846EBA5)
I (..\Components\fatfs\ffconf.h)(0x6846EBA5)
I (..\Components\fatfs\diskio.h)(0x6846EBA5)
I (..\APP\sd_app.h)(0x6846EBA5)
I (..\APP\led_app.h)(0x6846EBA5)
I (..\APP\adc_app.h)(0x6846EBA5)
I (..\APP\oled_app.h)(0x6846EBA5)
I (..\APP\usart_app.h)(0x6846EBA5)
I (..\APP\rtc_app.h)(0x6846EBA5)
I (..\APP\btn_app.h)(0x6846EDF1)
I (..\APP\scheduler.h)(0x6846EBA5)
I (C:\Keil_v5\GorgonMeducer\perf_counter\2.3.3\perf_counter.h)(0x6842F7C4)
I (C:\Keil_v5\GorgonMeducer\perf_counter\2.3.3\perfc_port_default.h)(0x6842F7C4)
I (C:\Keil_v5\ARM\CMSIS\6.1.0\CMSIS\Core\Include\cmsis_compiler.h)(0x664BD888)
F (..\APP\oled_app.c)(0x6846F66E)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O1 -ffunction-sections -w -I ../Libraries/Include -I ../USER/inc -I ../Driver/CMSIS/GD/GD32F4xx/Include -I ../Components/bsp -I ../Components/oled -I ../Components/gd25qxx -I ../Components/ebtn -I ../Components/sdio -I ../Components/fatfs -I ../APP

-I./RTE/_McuSTUDIO_F470VET6

-IC:/Keil_v5/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-IC:/Keil_v5/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-IC:/Keil_v5/GorgonMeducer/perf_counter/2.3.3

-D__UVISION_VERSION="541" -DGD32F470 -D_RTE_ -DUSE_STDPERIPH_DRIVER -DGD32F470  -include "Pre_Include_Global.h"

-o ./output/oled_app.o -MMD)
I (RTE\_McuSTUDIO_F470VET6\Pre_Include_Global.h)(0x6846EBF3)
I (..\Components\bsp\mcu_cmic_gd32f470vet6.h)(0x6846EBA5)
I (..\Driver\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x6846EBA6)
I (C:\Keil_v5\ARM\CMSIS\6.1.0\CMSIS\Core\Include\core_cm4.h)(0x664BD888)
I (..\Driver\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x6846EBA6)
I (..\USER\inc\gd32f4xx_libopt.h)(0x6846EBAD)
I (..\Libraries\Include\gd32f4xx_rcu.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_adc.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_can.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_crc.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_ctc.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_dac.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_dbg.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_dci.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_dma.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_exti.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_fmc.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_fwdgt.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_gpio.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_syscfg.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_i2c.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_iref.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_pmu.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_rtc.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_sdio.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_spi.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_timer.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_trng.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_usart.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_wwdgt.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_misc.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_enet.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_exmc.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_ipa.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_tli.h)(0x6846EBA7)
I (..\USER\inc\systick.h)(0x6846EBAD)
I (..\Components\ebtn\ebtn.h)(0x6846EBA5)
I (..\Components\ebtn\bit_array.h)(0x6846EBA5)
I (..\Components\oled\oled.h)(0x6846EBA6)
I (..\Components\gd25qxx\gd25qxx.h)(0x6846EBA6)
I (..\Components\sdio\sdio_sdcard.h)(0x6846EBA6)
I (..\Components\fatfs\ff.h)(0x6846EBA5)
I (..\Components\fatfs\integer.h)(0x6846EBA5)
I (..\Components\fatfs\ffconf.h)(0x6846EBA5)
I (..\Components\fatfs\diskio.h)(0x6846EBA5)
I (..\APP\sd_app.h)(0x6846EBA5)
I (..\APP\led_app.h)(0x6846EBA5)
I (..\APP\adc_app.h)(0x6846EBA5)
I (..\APP\oled_app.h)(0x6846EBA5)
I (..\APP\usart_app.h)(0x6846EBA5)
I (..\APP\rtc_app.h)(0x6846EBA5)
I (..\APP\btn_app.h)(0x6846EDF1)
I (..\APP\scheduler.h)(0x6846EBA5)
I (C:\Keil_v5\GorgonMeducer\perf_counter\2.3.3\perf_counter.h)(0x6842F7C4)
I (C:\Keil_v5\GorgonMeducer\perf_counter\2.3.3\perfc_port_default.h)(0x6842F7C4)
I (C:\Keil_v5\ARM\CMSIS\6.1.0\CMSIS\Core\Include\cmsis_compiler.h)(0x664BD888)
F (..\APP\scheduler.c)(0x6846EBA5)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O1 -ffunction-sections -w -I ../Libraries/Include -I ../USER/inc -I ../Driver/CMSIS/GD/GD32F4xx/Include -I ../Components/bsp -I ../Components/oled -I ../Components/gd25qxx -I ../Components/ebtn -I ../Components/sdio -I ../Components/fatfs -I ../APP

-I./RTE/_McuSTUDIO_F470VET6

-IC:/Keil_v5/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-IC:/Keil_v5/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-IC:/Keil_v5/GorgonMeducer/perf_counter/2.3.3

-D__UVISION_VERSION="541" -DGD32F470 -D_RTE_ -DUSE_STDPERIPH_DRIVER -DGD32F470  -include "Pre_Include_Global.h"

-o ./output/scheduler.o -MMD)
I (RTE\_McuSTUDIO_F470VET6\Pre_Include_Global.h)(0x6846EBF3)
I (..\Components\bsp\mcu_cmic_gd32f470vet6.h)(0x6846EBA5)
I (..\Driver\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x6846EBA6)
I (C:\Keil_v5\ARM\CMSIS\6.1.0\CMSIS\Core\Include\core_cm4.h)(0x664BD888)
I (..\Driver\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x6846EBA6)
I (..\USER\inc\gd32f4xx_libopt.h)(0x6846EBAD)
I (..\Libraries\Include\gd32f4xx_rcu.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_adc.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_can.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_crc.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_ctc.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_dac.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_dbg.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_dci.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_dma.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_exti.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_fmc.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_fwdgt.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_gpio.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_syscfg.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_i2c.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_iref.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_pmu.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_rtc.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_sdio.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_spi.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_timer.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_trng.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_usart.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_wwdgt.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_misc.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_enet.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_exmc.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_ipa.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_tli.h)(0x6846EBA7)
I (..\USER\inc\systick.h)(0x6846EBAD)
I (..\Components\ebtn\ebtn.h)(0x6846EBA5)
I (..\Components\ebtn\bit_array.h)(0x6846EBA5)
I (..\Components\oled\oled.h)(0x6846EBA6)
I (..\Components\gd25qxx\gd25qxx.h)(0x6846EBA6)
I (..\Components\sdio\sdio_sdcard.h)(0x6846EBA6)
I (..\Components\fatfs\ff.h)(0x6846EBA5)
I (..\Components\fatfs\integer.h)(0x6846EBA5)
I (..\Components\fatfs\ffconf.h)(0x6846EBA5)
I (..\Components\fatfs\diskio.h)(0x6846EBA5)
I (..\APP\sd_app.h)(0x6846EBA5)
I (..\APP\led_app.h)(0x6846EBA5)
I (..\APP\adc_app.h)(0x6846EBA5)
I (..\APP\oled_app.h)(0x6846EBA5)
I (..\APP\usart_app.h)(0x6846EBA5)
I (..\APP\rtc_app.h)(0x6846EBA5)
I (..\APP\btn_app.h)(0x6846EDF1)
I (..\APP\scheduler.h)(0x6846EBA5)
I (C:\Keil_v5\GorgonMeducer\perf_counter\2.3.3\perf_counter.h)(0x6842F7C4)
I (C:\Keil_v5\GorgonMeducer\perf_counter\2.3.3\perfc_port_default.h)(0x6842F7C4)
I (C:\Keil_v5\ARM\CMSIS\6.1.0\CMSIS\Core\Include\cmsis_compiler.h)(0x664BD888)
F (..\APP\usart_app.c)(0x6846EBA5)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O1 -ffunction-sections -w -I ../Libraries/Include -I ../USER/inc -I ../Driver/CMSIS/GD/GD32F4xx/Include -I ../Components/bsp -I ../Components/oled -I ../Components/gd25qxx -I ../Components/ebtn -I ../Components/sdio -I ../Components/fatfs -I ../APP

-I./RTE/_McuSTUDIO_F470VET6

-IC:/Keil_v5/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-IC:/Keil_v5/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-IC:/Keil_v5/GorgonMeducer/perf_counter/2.3.3

-D__UVISION_VERSION="541" -DGD32F470 -D_RTE_ -DUSE_STDPERIPH_DRIVER -DGD32F470  -include "Pre_Include_Global.h"

-o ./output/usart_app.o -MMD)
I (RTE\_McuSTUDIO_F470VET6\Pre_Include_Global.h)(0x6846EBF3)
I (..\Components\bsp\mcu_cmic_gd32f470vet6.h)(0x6846EBA5)
I (..\Driver\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x6846EBA6)
I (C:\Keil_v5\ARM\CMSIS\6.1.0\CMSIS\Core\Include\core_cm4.h)(0x664BD888)
I (..\Driver\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x6846EBA6)
I (..\USER\inc\gd32f4xx_libopt.h)(0x6846EBAD)
I (..\Libraries\Include\gd32f4xx_rcu.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_adc.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_can.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_crc.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_ctc.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_dac.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_dbg.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_dci.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_dma.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_exti.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_fmc.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_fwdgt.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_gpio.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_syscfg.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_i2c.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_iref.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_pmu.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_rtc.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_sdio.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_spi.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_timer.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_trng.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_usart.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_wwdgt.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_misc.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_enet.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_exmc.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_ipa.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_tli.h)(0x6846EBA7)
I (..\USER\inc\systick.h)(0x6846EBAD)
I (..\Components\ebtn\ebtn.h)(0x6846EBA5)
I (..\Components\ebtn\bit_array.h)(0x6846EBA5)
I (..\Components\oled\oled.h)(0x6846EBA6)
I (..\Components\gd25qxx\gd25qxx.h)(0x6846EBA6)
I (..\Components\sdio\sdio_sdcard.h)(0x6846EBA6)
I (..\Components\fatfs\ff.h)(0x6846EBA5)
I (..\Components\fatfs\integer.h)(0x6846EBA5)
I (..\Components\fatfs\ffconf.h)(0x6846EBA5)
I (..\Components\fatfs\diskio.h)(0x6846EBA5)
I (..\APP\sd_app.h)(0x6846EBA5)
I (..\APP\led_app.h)(0x6846EBA5)
I (..\APP\adc_app.h)(0x6846EBA5)
I (..\APP\oled_app.h)(0x6846EBA5)
I (..\APP\usart_app.h)(0x6846EBA5)
I (..\APP\rtc_app.h)(0x6846EBA5)
I (..\APP\btn_app.h)(0x6846EDF1)
I (..\APP\scheduler.h)(0x6846EBA5)
I (C:\Keil_v5\GorgonMeducer\perf_counter\2.3.3\perf_counter.h)(0x6842F7C4)
I (C:\Keil_v5\GorgonMeducer\perf_counter\2.3.3\perfc_port_default.h)(0x6842F7C4)
I (C:\Keil_v5\ARM\CMSIS\6.1.0\CMSIS\Core\Include\cmsis_compiler.h)(0x664BD888)
F (..\APP\sd_app.c)(0x6846EBA5)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O1 -ffunction-sections -w -I ../Libraries/Include -I ../USER/inc -I ../Driver/CMSIS/GD/GD32F4xx/Include -I ../Components/bsp -I ../Components/oled -I ../Components/gd25qxx -I ../Components/ebtn -I ../Components/sdio -I ../Components/fatfs -I ../APP

-I./RTE/_McuSTUDIO_F470VET6

-IC:/Keil_v5/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-IC:/Keil_v5/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-IC:/Keil_v5/GorgonMeducer/perf_counter/2.3.3

-D__UVISION_VERSION="541" -DGD32F470 -D_RTE_ -DUSE_STDPERIPH_DRIVER -DGD32F470  -include "Pre_Include_Global.h"

-o ./output/sd_app.o -MMD)
I (RTE\_McuSTUDIO_F470VET6\Pre_Include_Global.h)(0x6846EBF3)
I (..\Components\bsp\mcu_cmic_gd32f470vet6.h)(0x6846EBA5)
I (..\Driver\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x6846EBA6)
I (C:\Keil_v5\ARM\CMSIS\6.1.0\CMSIS\Core\Include\core_cm4.h)(0x664BD888)
I (..\Driver\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x6846EBA6)
I (..\USER\inc\gd32f4xx_libopt.h)(0x6846EBAD)
I (..\Libraries\Include\gd32f4xx_rcu.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_adc.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_can.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_crc.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_ctc.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_dac.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_dbg.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_dci.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_dma.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_exti.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_fmc.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_fwdgt.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_gpio.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_syscfg.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_i2c.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_iref.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_pmu.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_rtc.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_sdio.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_spi.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_timer.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_trng.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_usart.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_wwdgt.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_misc.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_enet.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_exmc.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_ipa.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_tli.h)(0x6846EBA7)
I (..\USER\inc\systick.h)(0x6846EBAD)
I (..\Components\ebtn\ebtn.h)(0x6846EBA5)
I (..\Components\ebtn\bit_array.h)(0x6846EBA5)
I (..\Components\oled\oled.h)(0x6846EBA6)
I (..\Components\gd25qxx\gd25qxx.h)(0x6846EBA6)
I (..\Components\sdio\sdio_sdcard.h)(0x6846EBA6)
I (..\Components\fatfs\ff.h)(0x6846EBA5)
I (..\Components\fatfs\integer.h)(0x6846EBA5)
I (..\Components\fatfs\ffconf.h)(0x6846EBA5)
I (..\Components\fatfs\diskio.h)(0x6846EBA5)
I (..\APP\sd_app.h)(0x6846EBA5)
I (..\APP\led_app.h)(0x6846EBA5)
I (..\APP\adc_app.h)(0x6846EBA5)
I (..\APP\oled_app.h)(0x6846EBA5)
I (..\APP\usart_app.h)(0x6846EBA5)
I (..\APP\rtc_app.h)(0x6846EBA5)
I (..\APP\btn_app.h)(0x6846EDF1)
I (..\APP\scheduler.h)(0x6846EBA5)
I (C:\Keil_v5\GorgonMeducer\perf_counter\2.3.3\perf_counter.h)(0x6842F7C4)
I (C:\Keil_v5\GorgonMeducer\perf_counter\2.3.3\perfc_port_default.h)(0x6842F7C4)
I (C:\Keil_v5\ARM\CMSIS\6.1.0\CMSIS\Core\Include\cmsis_compiler.h)(0x664BD888)
F (..\APP\rtc_app.c)(0x6846F2D9)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O1 -ffunction-sections -w -I ../Libraries/Include -I ../USER/inc -I ../Driver/CMSIS/GD/GD32F4xx/Include -I ../Components/bsp -I ../Components/oled -I ../Components/gd25qxx -I ../Components/ebtn -I ../Components/sdio -I ../Components/fatfs -I ../APP

-I./RTE/_McuSTUDIO_F470VET6

-IC:/Keil_v5/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-IC:/Keil_v5/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-IC:/Keil_v5/GorgonMeducer/perf_counter/2.3.3

-D__UVISION_VERSION="541" -DGD32F470 -D_RTE_ -DUSE_STDPERIPH_DRIVER -DGD32F470  -include "Pre_Include_Global.h"

-o ./output/rtc_app.o -MMD)
I (RTE\_McuSTUDIO_F470VET6\Pre_Include_Global.h)(0x6846EBF3)
I (..\Components\bsp\mcu_cmic_gd32f470vet6.h)(0x6846EBA5)
I (..\Driver\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x6846EBA6)
I (C:\Keil_v5\ARM\CMSIS\6.1.0\CMSIS\Core\Include\core_cm4.h)(0x664BD888)
I (..\Driver\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x6846EBA6)
I (..\USER\inc\gd32f4xx_libopt.h)(0x6846EBAD)
I (..\Libraries\Include\gd32f4xx_rcu.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_adc.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_can.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_crc.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_ctc.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_dac.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_dbg.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_dci.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_dma.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_exti.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_fmc.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_fwdgt.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_gpio.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_syscfg.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_i2c.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_iref.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_pmu.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_rtc.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_sdio.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_spi.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_timer.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_trng.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_usart.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_wwdgt.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_misc.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_enet.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_exmc.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_ipa.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_tli.h)(0x6846EBA7)
I (..\USER\inc\systick.h)(0x6846EBAD)
I (..\Components\ebtn\ebtn.h)(0x6846EBA5)
I (..\Components\ebtn\bit_array.h)(0x6846EBA5)
I (..\Components\oled\oled.h)(0x6846EBA6)
I (..\Components\gd25qxx\gd25qxx.h)(0x6846EBA6)
I (..\Components\sdio\sdio_sdcard.h)(0x6846EBA6)
I (..\Components\fatfs\ff.h)(0x6846EBA5)
I (..\Components\fatfs\integer.h)(0x6846EBA5)
I (..\Components\fatfs\ffconf.h)(0x6846EBA5)
I (..\Components\fatfs\diskio.h)(0x6846EBA5)
I (..\APP\sd_app.h)(0x6846EBA5)
I (..\APP\led_app.h)(0x6846EBA5)
I (..\APP\adc_app.h)(0x6846EBA5)
I (..\APP\oled_app.h)(0x6846EBA5)
I (..\APP\usart_app.h)(0x6846EBA5)
I (..\APP\rtc_app.h)(0x6846EBA5)
I (..\APP\btn_app.h)(0x6846EDF1)
I (..\APP\scheduler.h)(0x6846EBA5)
I (C:\Keil_v5\GorgonMeducer\perf_counter\2.3.3\perf_counter.h)(0x6842F7C4)
I (C:\Keil_v5\GorgonMeducer\perf_counter\2.3.3\perfc_port_default.h)(0x6842F7C4)
I (C:\Keil_v5\ARM\CMSIS\6.1.0\CMSIS\Core\Include\cmsis_compiler.h)(0x664BD888)
F (..\APP\rtc_app.h)(0x6846EBA5)()
F (..\APP\adc_app.c)(0x6846EBA5)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O1 -ffunction-sections -w -I ../Libraries/Include -I ../USER/inc -I ../Driver/CMSIS/GD/GD32F4xx/Include -I ../Components/bsp -I ../Components/oled -I ../Components/gd25qxx -I ../Components/ebtn -I ../Components/sdio -I ../Components/fatfs -I ../APP

-I./RTE/_McuSTUDIO_F470VET6

-IC:/Keil_v5/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-IC:/Keil_v5/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-IC:/Keil_v5/GorgonMeducer/perf_counter/2.3.3

-D__UVISION_VERSION="541" -DGD32F470 -D_RTE_ -DUSE_STDPERIPH_DRIVER -DGD32F470  -include "Pre_Include_Global.h"

-o ./output/adc_app.o -MMD)
I (RTE\_McuSTUDIO_F470VET6\Pre_Include_Global.h)(0x6846EBF3)
I (..\Components\bsp\mcu_cmic_gd32f470vet6.h)(0x6846EBA5)
I (..\Driver\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x6846EBA6)
I (C:\Keil_v5\ARM\CMSIS\6.1.0\CMSIS\Core\Include\core_cm4.h)(0x664BD888)
I (..\Driver\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x6846EBA6)
I (..\USER\inc\gd32f4xx_libopt.h)(0x6846EBAD)
I (..\Libraries\Include\gd32f4xx_rcu.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_adc.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_can.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_crc.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_ctc.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_dac.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_dbg.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_dci.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_dma.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_exti.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_fmc.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_fwdgt.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_gpio.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_syscfg.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_i2c.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_iref.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_pmu.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_rtc.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_sdio.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_spi.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_timer.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_trng.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_usart.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_wwdgt.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_misc.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_enet.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_exmc.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_ipa.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_tli.h)(0x6846EBA7)
I (..\USER\inc\systick.h)(0x6846EBAD)
I (..\Components\ebtn\ebtn.h)(0x6846EBA5)
I (..\Components\ebtn\bit_array.h)(0x6846EBA5)
I (..\Components\oled\oled.h)(0x6846EBA6)
I (..\Components\gd25qxx\gd25qxx.h)(0x6846EBA6)
I (..\Components\sdio\sdio_sdcard.h)(0x6846EBA6)
I (..\Components\fatfs\ff.h)(0x6846EBA5)
I (..\Components\fatfs\integer.h)(0x6846EBA5)
I (..\Components\fatfs\ffconf.h)(0x6846EBA5)
I (..\Components\fatfs\diskio.h)(0x6846EBA5)
I (..\APP\sd_app.h)(0x6846EBA5)
I (..\APP\led_app.h)(0x6846EBA5)
I (..\APP\adc_app.h)(0x6846EBA5)
I (..\APP\oled_app.h)(0x6846EBA5)
I (..\APP\usart_app.h)(0x6846EBA5)
I (..\APP\rtc_app.h)(0x6846EBA5)
I (..\APP\btn_app.h)(0x6846EDF1)
I (..\APP\scheduler.h)(0x6846EBA5)
I (C:\Keil_v5\GorgonMeducer\perf_counter\2.3.3\perf_counter.h)(0x6842F7C4)
I (C:\Keil_v5\GorgonMeducer\perf_counter\2.3.3\perfc_port_default.h)(0x6842F7C4)
I (C:\Keil_v5\ARM\CMSIS\6.1.0\CMSIS\Core\Include\cmsis_compiler.h)(0x664BD888)
F (..\Libraries\Source\gd32f4xx_adc.c)(0x6846EBA7)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O1 -ffunction-sections -w -I ../Libraries/Include -I ../USER/inc -I ../Driver/CMSIS/GD/GD32F4xx/Include -I ../Components/bsp -I ../Components/oled -I ../Components/gd25qxx -I ../Components/ebtn -I ../Components/sdio -I ../Components/fatfs -I ../APP

-I./RTE/_McuSTUDIO_F470VET6

-IC:/Keil_v5/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-IC:/Keil_v5/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-IC:/Keil_v5/GorgonMeducer/perf_counter/2.3.3

-D__UVISION_VERSION="541" -DGD32F470 -D_RTE_ -DUSE_STDPERIPH_DRIVER -DGD32F470  -include "Pre_Include_Global.h"

-o ./output/gd32f4xx_adc.o -MMD)
I (RTE\_McuSTUDIO_F470VET6\Pre_Include_Global.h)(0x6846EBF3)
I (..\Libraries\Include\gd32f4xx_adc.h)(0x6846EBA7)
I (..\Driver\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x6846EBA6)
I (C:\Keil_v5\ARM\CMSIS\6.1.0\CMSIS\Core\Include\core_cm4.h)(0x664BD888)
I (..\Driver\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x6846EBA6)
I (..\USER\inc\gd32f4xx_libopt.h)(0x6846EBAD)
I (..\Libraries\Include\gd32f4xx_rcu.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_can.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_crc.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_ctc.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_dac.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_dbg.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_dci.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_dma.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_exti.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_fmc.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_fwdgt.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_gpio.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_syscfg.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_i2c.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_iref.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_pmu.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_rtc.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_sdio.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_spi.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_timer.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_trng.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_usart.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_wwdgt.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_misc.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_enet.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_exmc.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_ipa.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_tli.h)(0x6846EBA7)
F (..\Libraries\Source\gd32f4xx_can.c)(0x6846EBA7)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O1 -ffunction-sections -w -I ../Libraries/Include -I ../USER/inc -I ../Driver/CMSIS/GD/GD32F4xx/Include -I ../Components/bsp -I ../Components/oled -I ../Components/gd25qxx -I ../Components/ebtn -I ../Components/sdio -I ../Components/fatfs -I ../APP

-I./RTE/_McuSTUDIO_F470VET6

-IC:/Keil_v5/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-IC:/Keil_v5/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-IC:/Keil_v5/GorgonMeducer/perf_counter/2.3.3

-D__UVISION_VERSION="541" -DGD32F470 -D_RTE_ -DUSE_STDPERIPH_DRIVER -DGD32F470  -include "Pre_Include_Global.h"

-o ./output/gd32f4xx_can.o -MMD)
I (RTE\_McuSTUDIO_F470VET6\Pre_Include_Global.h)(0x6846EBF3)
I (..\Libraries\Include\gd32f4xx_can.h)(0x6846EBA7)
I (..\Driver\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x6846EBA6)
I (C:\Keil_v5\ARM\CMSIS\6.1.0\CMSIS\Core\Include\core_cm4.h)(0x664BD888)
I (..\Driver\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x6846EBA6)
I (..\USER\inc\gd32f4xx_libopt.h)(0x6846EBAD)
I (..\Libraries\Include\gd32f4xx_rcu.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_adc.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_crc.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_ctc.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_dac.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_dbg.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_dci.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_dma.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_exti.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_fmc.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_fwdgt.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_gpio.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_syscfg.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_i2c.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_iref.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_pmu.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_rtc.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_sdio.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_spi.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_timer.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_trng.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_usart.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_wwdgt.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_misc.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_enet.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_exmc.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_ipa.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_tli.h)(0x6846EBA7)
F (..\Libraries\Source\gd32f4xx_crc.c)(0x6846EBA7)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O1 -ffunction-sections -w -I ../Libraries/Include -I ../USER/inc -I ../Driver/CMSIS/GD/GD32F4xx/Include -I ../Components/bsp -I ../Components/oled -I ../Components/gd25qxx -I ../Components/ebtn -I ../Components/sdio -I ../Components/fatfs -I ../APP

-I./RTE/_McuSTUDIO_F470VET6

-IC:/Keil_v5/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-IC:/Keil_v5/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-IC:/Keil_v5/GorgonMeducer/perf_counter/2.3.3

-D__UVISION_VERSION="541" -DGD32F470 -D_RTE_ -DUSE_STDPERIPH_DRIVER -DGD32F470  -include "Pre_Include_Global.h"

-o ./output/gd32f4xx_crc.o -MMD)
I (RTE\_McuSTUDIO_F470VET6\Pre_Include_Global.h)(0x6846EBF3)
I (..\Libraries\Include\gd32f4xx_crc.h)(0x6846EBA7)
I (..\Driver\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x6846EBA6)
I (C:\Keil_v5\ARM\CMSIS\6.1.0\CMSIS\Core\Include\core_cm4.h)(0x664BD888)
I (..\Driver\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x6846EBA6)
I (..\USER\inc\gd32f4xx_libopt.h)(0x6846EBAD)
I (..\Libraries\Include\gd32f4xx_rcu.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_adc.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_can.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_ctc.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_dac.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_dbg.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_dci.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_dma.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_exti.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_fmc.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_fwdgt.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_gpio.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_syscfg.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_i2c.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_iref.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_pmu.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_rtc.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_sdio.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_spi.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_timer.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_trng.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_usart.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_wwdgt.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_misc.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_enet.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_exmc.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_ipa.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_tli.h)(0x6846EBA7)
F (..\Libraries\Source\gd32f4xx_ctc.c)(0x6846EBA7)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O1 -ffunction-sections -w -I ../Libraries/Include -I ../USER/inc -I ../Driver/CMSIS/GD/GD32F4xx/Include -I ../Components/bsp -I ../Components/oled -I ../Components/gd25qxx -I ../Components/ebtn -I ../Components/sdio -I ../Components/fatfs -I ../APP

-I./RTE/_McuSTUDIO_F470VET6

-IC:/Keil_v5/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-IC:/Keil_v5/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-IC:/Keil_v5/GorgonMeducer/perf_counter/2.3.3

-D__UVISION_VERSION="541" -DGD32F470 -D_RTE_ -DUSE_STDPERIPH_DRIVER -DGD32F470  -include "Pre_Include_Global.h"

-o ./output/gd32f4xx_ctc.o -MMD)
I (RTE\_McuSTUDIO_F470VET6\Pre_Include_Global.h)(0x6846EBF3)
I (..\Libraries\Include\gd32f4xx_ctc.h)(0x6846EBA7)
I (..\Driver\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x6846EBA6)
I (C:\Keil_v5\ARM\CMSIS\6.1.0\CMSIS\Core\Include\core_cm4.h)(0x664BD888)
I (..\Driver\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x6846EBA6)
I (..\USER\inc\gd32f4xx_libopt.h)(0x6846EBAD)
I (..\Libraries\Include\gd32f4xx_rcu.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_adc.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_can.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_crc.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_dac.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_dbg.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_dci.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_dma.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_exti.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_fmc.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_fwdgt.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_gpio.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_syscfg.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_i2c.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_iref.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_pmu.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_rtc.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_sdio.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_spi.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_timer.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_trng.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_usart.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_wwdgt.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_misc.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_enet.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_exmc.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_ipa.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_tli.h)(0x6846EBA7)
F (..\Libraries\Source\gd32f4xx_dac.c)(0x6846EBA7)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O1 -ffunction-sections -w -I ../Libraries/Include -I ../USER/inc -I ../Driver/CMSIS/GD/GD32F4xx/Include -I ../Components/bsp -I ../Components/oled -I ../Components/gd25qxx -I ../Components/ebtn -I ../Components/sdio -I ../Components/fatfs -I ../APP

-I./RTE/_McuSTUDIO_F470VET6

-IC:/Keil_v5/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-IC:/Keil_v5/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-IC:/Keil_v5/GorgonMeducer/perf_counter/2.3.3

-D__UVISION_VERSION="541" -DGD32F470 -D_RTE_ -DUSE_STDPERIPH_DRIVER -DGD32F470  -include "Pre_Include_Global.h"

-o ./output/gd32f4xx_dac.o -MMD)
I (RTE\_McuSTUDIO_F470VET6\Pre_Include_Global.h)(0x6846EBF3)
I (..\Libraries\Include\gd32f4xx_dac.h)(0x6846EBA7)
I (..\Driver\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x6846EBA6)
I (C:\Keil_v5\ARM\CMSIS\6.1.0\CMSIS\Core\Include\core_cm4.h)(0x664BD888)
I (..\Driver\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x6846EBA6)
I (..\USER\inc\gd32f4xx_libopt.h)(0x6846EBAD)
I (..\Libraries\Include\gd32f4xx_rcu.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_adc.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_can.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_crc.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_ctc.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_dbg.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_dci.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_dma.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_exti.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_fmc.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_fwdgt.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_gpio.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_syscfg.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_i2c.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_iref.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_pmu.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_rtc.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_sdio.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_spi.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_timer.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_trng.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_usart.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_wwdgt.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_misc.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_enet.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_exmc.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_ipa.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_tli.h)(0x6846EBA7)
F (..\Libraries\Source\gd32f4xx_dbg.c)(0x6846EBA7)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O1 -ffunction-sections -w -I ../Libraries/Include -I ../USER/inc -I ../Driver/CMSIS/GD/GD32F4xx/Include -I ../Components/bsp -I ../Components/oled -I ../Components/gd25qxx -I ../Components/ebtn -I ../Components/sdio -I ../Components/fatfs -I ../APP

-I./RTE/_McuSTUDIO_F470VET6

-IC:/Keil_v5/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-IC:/Keil_v5/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-IC:/Keil_v5/GorgonMeducer/perf_counter/2.3.3

-D__UVISION_VERSION="541" -DGD32F470 -D_RTE_ -DUSE_STDPERIPH_DRIVER -DGD32F470  -include "Pre_Include_Global.h"

-o ./output/gd32f4xx_dbg.o -MMD)
I (RTE\_McuSTUDIO_F470VET6\Pre_Include_Global.h)(0x6846EBF3)
I (..\Libraries\Include\gd32f4xx_dbg.h)(0x6846EBA7)
I (..\Driver\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x6846EBA6)
I (C:\Keil_v5\ARM\CMSIS\6.1.0\CMSIS\Core\Include\core_cm4.h)(0x664BD888)
I (..\Driver\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x6846EBA6)
I (..\USER\inc\gd32f4xx_libopt.h)(0x6846EBAD)
I (..\Libraries\Include\gd32f4xx_rcu.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_adc.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_can.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_crc.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_ctc.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_dac.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_dci.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_dma.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_exti.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_fmc.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_fwdgt.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_gpio.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_syscfg.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_i2c.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_iref.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_pmu.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_rtc.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_sdio.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_spi.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_timer.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_trng.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_usart.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_wwdgt.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_misc.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_enet.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_exmc.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_ipa.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_tli.h)(0x6846EBA7)
F (..\Libraries\Source\gd32f4xx_dci.c)(0x6846EBA7)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O1 -ffunction-sections -w -I ../Libraries/Include -I ../USER/inc -I ../Driver/CMSIS/GD/GD32F4xx/Include -I ../Components/bsp -I ../Components/oled -I ../Components/gd25qxx -I ../Components/ebtn -I ../Components/sdio -I ../Components/fatfs -I ../APP

-I./RTE/_McuSTUDIO_F470VET6

-IC:/Keil_v5/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-IC:/Keil_v5/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-IC:/Keil_v5/GorgonMeducer/perf_counter/2.3.3

-D__UVISION_VERSION="541" -DGD32F470 -D_RTE_ -DUSE_STDPERIPH_DRIVER -DGD32F470  -include "Pre_Include_Global.h"

-o ./output/gd32f4xx_dci.o -MMD)
I (RTE\_McuSTUDIO_F470VET6\Pre_Include_Global.h)(0x6846EBF3)
I (..\Libraries\Include\gd32f4xx_dci.h)(0x6846EBA7)
I (..\Driver\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x6846EBA6)
I (C:\Keil_v5\ARM\CMSIS\6.1.0\CMSIS\Core\Include\core_cm4.h)(0x664BD888)
I (..\Driver\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x6846EBA6)
I (..\USER\inc\gd32f4xx_libopt.h)(0x6846EBAD)
I (..\Libraries\Include\gd32f4xx_rcu.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_adc.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_can.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_crc.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_ctc.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_dac.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_dbg.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_dma.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_exti.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_fmc.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_fwdgt.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_gpio.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_syscfg.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_i2c.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_iref.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_pmu.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_rtc.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_sdio.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_spi.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_timer.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_trng.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_usart.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_wwdgt.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_misc.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_enet.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_exmc.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_ipa.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_tli.h)(0x6846EBA7)
F (..\Libraries\Source\gd32f4xx_dma.c)(0x6846EBA7)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O1 -ffunction-sections -w -I ../Libraries/Include -I ../USER/inc -I ../Driver/CMSIS/GD/GD32F4xx/Include -I ../Components/bsp -I ../Components/oled -I ../Components/gd25qxx -I ../Components/ebtn -I ../Components/sdio -I ../Components/fatfs -I ../APP

-I./RTE/_McuSTUDIO_F470VET6

-IC:/Keil_v5/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-IC:/Keil_v5/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-IC:/Keil_v5/GorgonMeducer/perf_counter/2.3.3

-D__UVISION_VERSION="541" -DGD32F470 -D_RTE_ -DUSE_STDPERIPH_DRIVER -DGD32F470  -include "Pre_Include_Global.h"

-o ./output/gd32f4xx_dma.o -MMD)
I (RTE\_McuSTUDIO_F470VET6\Pre_Include_Global.h)(0x6846EBF3)
I (..\Libraries\Include\gd32f4xx_dma.h)(0x6846EBA7)
I (..\Driver\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x6846EBA6)
I (C:\Keil_v5\ARM\CMSIS\6.1.0\CMSIS\Core\Include\core_cm4.h)(0x664BD888)
I (..\Driver\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x6846EBA6)
I (..\USER\inc\gd32f4xx_libopt.h)(0x6846EBAD)
I (..\Libraries\Include\gd32f4xx_rcu.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_adc.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_can.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_crc.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_ctc.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_dac.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_dbg.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_dci.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_exti.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_fmc.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_fwdgt.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_gpio.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_syscfg.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_i2c.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_iref.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_pmu.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_rtc.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_sdio.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_spi.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_timer.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_trng.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_usart.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_wwdgt.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_misc.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_enet.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_exmc.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_ipa.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_tli.h)(0x6846EBA7)
F (..\Libraries\Source\gd32f4xx_enet.c)(0x6846EBA7)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O1 -ffunction-sections -w -I ../Libraries/Include -I ../USER/inc -I ../Driver/CMSIS/GD/GD32F4xx/Include -I ../Components/bsp -I ../Components/oled -I ../Components/gd25qxx -I ../Components/ebtn -I ../Components/sdio -I ../Components/fatfs -I ../APP

-I./RTE/_McuSTUDIO_F470VET6

-IC:/Keil_v5/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-IC:/Keil_v5/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-IC:/Keil_v5/GorgonMeducer/perf_counter/2.3.3

-D__UVISION_VERSION="541" -DGD32F470 -D_RTE_ -DUSE_STDPERIPH_DRIVER -DGD32F470  -include "Pre_Include_Global.h"

-o ./output/gd32f4xx_enet.o -MMD)
I (RTE\_McuSTUDIO_F470VET6\Pre_Include_Global.h)(0x6846EBF3)
I (..\Libraries\Include\gd32f4xx_enet.h)(0x6846EBA7)
I (..\Driver\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x6846EBA6)
I (C:\Keil_v5\ARM\CMSIS\6.1.0\CMSIS\Core\Include\core_cm4.h)(0x664BD888)
I (..\Driver\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x6846EBA6)
I (..\USER\inc\gd32f4xx_libopt.h)(0x6846EBAD)
I (..\Libraries\Include\gd32f4xx_rcu.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_adc.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_can.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_crc.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_ctc.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_dac.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_dbg.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_dci.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_dma.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_exti.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_fmc.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_fwdgt.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_gpio.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_syscfg.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_i2c.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_iref.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_pmu.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_rtc.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_sdio.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_spi.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_timer.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_trng.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_usart.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_wwdgt.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_misc.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_exmc.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_ipa.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_tli.h)(0x6846EBA7)
F (..\Libraries\Source\gd32f4xx_exmc.c)(0x6846EBA7)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O1 -ffunction-sections -w -I ../Libraries/Include -I ../USER/inc -I ../Driver/CMSIS/GD/GD32F4xx/Include -I ../Components/bsp -I ../Components/oled -I ../Components/gd25qxx -I ../Components/ebtn -I ../Components/sdio -I ../Components/fatfs -I ../APP

-I./RTE/_McuSTUDIO_F470VET6

-IC:/Keil_v5/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-IC:/Keil_v5/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-IC:/Keil_v5/GorgonMeducer/perf_counter/2.3.3

-D__UVISION_VERSION="541" -DGD32F470 -D_RTE_ -DUSE_STDPERIPH_DRIVER -DGD32F470  -include "Pre_Include_Global.h"

-o ./output/gd32f4xx_exmc.o -MMD)
I (RTE\_McuSTUDIO_F470VET6\Pre_Include_Global.h)(0x6846EBF3)
I (..\Libraries\Include\gd32f4xx_exmc.h)(0x6846EBA7)
I (..\Driver\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x6846EBA6)
I (C:\Keil_v5\ARM\CMSIS\6.1.0\CMSIS\Core\Include\core_cm4.h)(0x664BD888)
I (..\Driver\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x6846EBA6)
I (..\USER\inc\gd32f4xx_libopt.h)(0x6846EBAD)
I (..\Libraries\Include\gd32f4xx_rcu.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_adc.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_can.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_crc.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_ctc.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_dac.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_dbg.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_dci.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_dma.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_exti.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_fmc.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_fwdgt.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_gpio.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_syscfg.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_i2c.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_iref.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_pmu.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_rtc.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_sdio.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_spi.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_timer.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_trng.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_usart.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_wwdgt.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_misc.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_enet.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_ipa.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_tli.h)(0x6846EBA7)
F (..\Libraries\Source\gd32f4xx_exti.c)(0x6846EBA7)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O1 -ffunction-sections -w -I ../Libraries/Include -I ../USER/inc -I ../Driver/CMSIS/GD/GD32F4xx/Include -I ../Components/bsp -I ../Components/oled -I ../Components/gd25qxx -I ../Components/ebtn -I ../Components/sdio -I ../Components/fatfs -I ../APP

-I./RTE/_McuSTUDIO_F470VET6

-IC:/Keil_v5/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-IC:/Keil_v5/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-IC:/Keil_v5/GorgonMeducer/perf_counter/2.3.3

-D__UVISION_VERSION="541" -DGD32F470 -D_RTE_ -DUSE_STDPERIPH_DRIVER -DGD32F470  -include "Pre_Include_Global.h"

-o ./output/gd32f4xx_exti.o -MMD)
I (RTE\_McuSTUDIO_F470VET6\Pre_Include_Global.h)(0x6846EBF3)
I (..\Libraries\Include\gd32f4xx_exti.h)(0x6846EBA7)
I (..\Driver\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x6846EBA6)
I (C:\Keil_v5\ARM\CMSIS\6.1.0\CMSIS\Core\Include\core_cm4.h)(0x664BD888)
I (..\Driver\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x6846EBA6)
I (..\USER\inc\gd32f4xx_libopt.h)(0x6846EBAD)
I (..\Libraries\Include\gd32f4xx_rcu.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_adc.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_can.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_crc.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_ctc.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_dac.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_dbg.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_dci.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_dma.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_fmc.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_fwdgt.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_gpio.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_syscfg.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_i2c.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_iref.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_pmu.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_rtc.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_sdio.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_spi.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_timer.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_trng.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_usart.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_wwdgt.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_misc.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_enet.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_exmc.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_ipa.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_tli.h)(0x6846EBA7)
F (..\Libraries\Source\gd32f4xx_fmc.c)(0x6846EBA7)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O1 -ffunction-sections -w -I ../Libraries/Include -I ../USER/inc -I ../Driver/CMSIS/GD/GD32F4xx/Include -I ../Components/bsp -I ../Components/oled -I ../Components/gd25qxx -I ../Components/ebtn -I ../Components/sdio -I ../Components/fatfs -I ../APP

-I./RTE/_McuSTUDIO_F470VET6

-IC:/Keil_v5/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-IC:/Keil_v5/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-IC:/Keil_v5/GorgonMeducer/perf_counter/2.3.3

-D__UVISION_VERSION="541" -DGD32F470 -D_RTE_ -DUSE_STDPERIPH_DRIVER -DGD32F470  -include "Pre_Include_Global.h"

-o ./output/gd32f4xx_fmc.o -MMD)
I (RTE\_McuSTUDIO_F470VET6\Pre_Include_Global.h)(0x6846EBF3)
I (..\Libraries\Include\gd32f4xx_fmc.h)(0x6846EBA7)
I (..\Driver\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x6846EBA6)
I (C:\Keil_v5\ARM\CMSIS\6.1.0\CMSIS\Core\Include\core_cm4.h)(0x664BD888)
I (..\Driver\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x6846EBA6)
I (..\USER\inc\gd32f4xx_libopt.h)(0x6846EBAD)
I (..\Libraries\Include\gd32f4xx_rcu.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_adc.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_can.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_crc.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_ctc.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_dac.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_dbg.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_dci.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_dma.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_exti.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_fwdgt.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_gpio.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_syscfg.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_i2c.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_iref.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_pmu.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_rtc.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_sdio.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_spi.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_timer.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_trng.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_usart.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_wwdgt.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_misc.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_enet.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_exmc.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_ipa.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_tli.h)(0x6846EBA7)
F (..\Libraries\Source\gd32f4xx_fwdgt.c)(0x6846EBA7)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O1 -ffunction-sections -w -I ../Libraries/Include -I ../USER/inc -I ../Driver/CMSIS/GD/GD32F4xx/Include -I ../Components/bsp -I ../Components/oled -I ../Components/gd25qxx -I ../Components/ebtn -I ../Components/sdio -I ../Components/fatfs -I ../APP

-I./RTE/_McuSTUDIO_F470VET6

-IC:/Keil_v5/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-IC:/Keil_v5/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-IC:/Keil_v5/GorgonMeducer/perf_counter/2.3.3

-D__UVISION_VERSION="541" -DGD32F470 -D_RTE_ -DUSE_STDPERIPH_DRIVER -DGD32F470  -include "Pre_Include_Global.h"

-o ./output/gd32f4xx_fwdgt.o -MMD)
I (RTE\_McuSTUDIO_F470VET6\Pre_Include_Global.h)(0x6846EBF3)
I (..\Libraries\Include\gd32f4xx_fwdgt.h)(0x6846EBA7)
I (..\Driver\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x6846EBA6)
I (C:\Keil_v5\ARM\CMSIS\6.1.0\CMSIS\Core\Include\core_cm4.h)(0x664BD888)
I (..\Driver\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x6846EBA6)
I (..\USER\inc\gd32f4xx_libopt.h)(0x6846EBAD)
I (..\Libraries\Include\gd32f4xx_rcu.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_adc.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_can.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_crc.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_ctc.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_dac.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_dbg.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_dci.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_dma.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_exti.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_fmc.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_gpio.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_syscfg.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_i2c.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_iref.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_pmu.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_rtc.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_sdio.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_spi.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_timer.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_trng.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_usart.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_wwdgt.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_misc.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_enet.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_exmc.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_ipa.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_tli.h)(0x6846EBA7)
F (..\Libraries\Source\gd32f4xx_gpio.c)(0x6846EBA7)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O1 -ffunction-sections -w -I ../Libraries/Include -I ../USER/inc -I ../Driver/CMSIS/GD/GD32F4xx/Include -I ../Components/bsp -I ../Components/oled -I ../Components/gd25qxx -I ../Components/ebtn -I ../Components/sdio -I ../Components/fatfs -I ../APP

-I./RTE/_McuSTUDIO_F470VET6

-IC:/Keil_v5/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-IC:/Keil_v5/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-IC:/Keil_v5/GorgonMeducer/perf_counter/2.3.3

-D__UVISION_VERSION="541" -DGD32F470 -D_RTE_ -DUSE_STDPERIPH_DRIVER -DGD32F470  -include "Pre_Include_Global.h"

-o ./output/gd32f4xx_gpio.o -MMD)
I (RTE\_McuSTUDIO_F470VET6\Pre_Include_Global.h)(0x6846EBF3)
I (..\Libraries\Include\gd32f4xx_gpio.h)(0x6846EBA7)
I (..\Driver\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x6846EBA6)
I (C:\Keil_v5\ARM\CMSIS\6.1.0\CMSIS\Core\Include\core_cm4.h)(0x664BD888)
I (..\Driver\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x6846EBA6)
I (..\USER\inc\gd32f4xx_libopt.h)(0x6846EBAD)
I (..\Libraries\Include\gd32f4xx_rcu.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_adc.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_can.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_crc.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_ctc.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_dac.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_dbg.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_dci.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_dma.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_exti.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_fmc.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_fwdgt.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_syscfg.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_i2c.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_iref.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_pmu.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_rtc.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_sdio.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_spi.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_timer.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_trng.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_usart.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_wwdgt.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_misc.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_enet.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_exmc.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_ipa.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_tli.h)(0x6846EBA7)
F (..\Libraries\Source\gd32f4xx_i2c.c)(0x6846EBA8)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O1 -ffunction-sections -w -I ../Libraries/Include -I ../USER/inc -I ../Driver/CMSIS/GD/GD32F4xx/Include -I ../Components/bsp -I ../Components/oled -I ../Components/gd25qxx -I ../Components/ebtn -I ../Components/sdio -I ../Components/fatfs -I ../APP

-I./RTE/_McuSTUDIO_F470VET6

-IC:/Keil_v5/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-IC:/Keil_v5/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-IC:/Keil_v5/GorgonMeducer/perf_counter/2.3.3

-D__UVISION_VERSION="541" -DGD32F470 -D_RTE_ -DUSE_STDPERIPH_DRIVER -DGD32F470  -include "Pre_Include_Global.h"

-o ./output/gd32f4xx_i2c.o -MMD)
I (RTE\_McuSTUDIO_F470VET6\Pre_Include_Global.h)(0x6846EBF3)
I (..\Libraries\Include\gd32f4xx_i2c.h)(0x6846EBA7)
I (..\Driver\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x6846EBA6)
I (C:\Keil_v5\ARM\CMSIS\6.1.0\CMSIS\Core\Include\core_cm4.h)(0x664BD888)
I (..\Driver\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x6846EBA6)
I (..\USER\inc\gd32f4xx_libopt.h)(0x6846EBAD)
I (..\Libraries\Include\gd32f4xx_rcu.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_adc.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_can.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_crc.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_ctc.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_dac.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_dbg.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_dci.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_dma.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_exti.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_fmc.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_fwdgt.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_gpio.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_syscfg.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_iref.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_pmu.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_rtc.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_sdio.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_spi.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_timer.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_trng.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_usart.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_wwdgt.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_misc.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_enet.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_exmc.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_ipa.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_tli.h)(0x6846EBA7)
F (..\Libraries\Source\gd32f4xx_ipa.c)(0x6846EBA8)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O1 -ffunction-sections -w -I ../Libraries/Include -I ../USER/inc -I ../Driver/CMSIS/GD/GD32F4xx/Include -I ../Components/bsp -I ../Components/oled -I ../Components/gd25qxx -I ../Components/ebtn -I ../Components/sdio -I ../Components/fatfs -I ../APP

-I./RTE/_McuSTUDIO_F470VET6

-IC:/Keil_v5/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-IC:/Keil_v5/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-IC:/Keil_v5/GorgonMeducer/perf_counter/2.3.3

-D__UVISION_VERSION="541" -DGD32F470 -D_RTE_ -DUSE_STDPERIPH_DRIVER -DGD32F470  -include "Pre_Include_Global.h"

-o ./output/gd32f4xx_ipa.o -MMD)
I (RTE\_McuSTUDIO_F470VET6\Pre_Include_Global.h)(0x6846EBF3)
I (..\Libraries\Include\gd32f4xx_ipa.h)(0x6846EBA7)
I (..\Driver\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x6846EBA6)
I (C:\Keil_v5\ARM\CMSIS\6.1.0\CMSIS\Core\Include\core_cm4.h)(0x664BD888)
I (..\Driver\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x6846EBA6)
I (..\USER\inc\gd32f4xx_libopt.h)(0x6846EBAD)
I (..\Libraries\Include\gd32f4xx_rcu.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_adc.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_can.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_crc.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_ctc.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_dac.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_dbg.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_dci.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_dma.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_exti.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_fmc.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_fwdgt.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_gpio.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_syscfg.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_i2c.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_iref.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_pmu.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_rtc.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_sdio.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_spi.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_timer.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_trng.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_usart.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_wwdgt.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_misc.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_enet.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_exmc.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_tli.h)(0x6846EBA7)
F (..\Libraries\Source\gd32f4xx_iref.c)(0x6846EBA8)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O1 -ffunction-sections -w -I ../Libraries/Include -I ../USER/inc -I ../Driver/CMSIS/GD/GD32F4xx/Include -I ../Components/bsp -I ../Components/oled -I ../Components/gd25qxx -I ../Components/ebtn -I ../Components/sdio -I ../Components/fatfs -I ../APP

-I./RTE/_McuSTUDIO_F470VET6

-IC:/Keil_v5/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-IC:/Keil_v5/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-IC:/Keil_v5/GorgonMeducer/perf_counter/2.3.3

-D__UVISION_VERSION="541" -DGD32F470 -D_RTE_ -DUSE_STDPERIPH_DRIVER -DGD32F470  -include "Pre_Include_Global.h"

-o ./output/gd32f4xx_iref.o -MMD)
I (RTE\_McuSTUDIO_F470VET6\Pre_Include_Global.h)(0x6846EBF3)
I (..\Libraries\Include\gd32f4xx_iref.h)(0x6846EBA7)
I (..\Driver\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x6846EBA6)
I (C:\Keil_v5\ARM\CMSIS\6.1.0\CMSIS\Core\Include\core_cm4.h)(0x664BD888)
I (..\Driver\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x6846EBA6)
I (..\USER\inc\gd32f4xx_libopt.h)(0x6846EBAD)
I (..\Libraries\Include\gd32f4xx_rcu.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_adc.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_can.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_crc.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_ctc.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_dac.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_dbg.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_dci.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_dma.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_exti.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_fmc.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_fwdgt.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_gpio.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_syscfg.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_i2c.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_pmu.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_rtc.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_sdio.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_spi.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_timer.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_trng.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_usart.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_wwdgt.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_misc.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_enet.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_exmc.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_ipa.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_tli.h)(0x6846EBA7)
F (..\Libraries\Source\gd32f4xx_misc.c)(0x6846EC68)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O1 -ffunction-sections -w -I ../Libraries/Include -I ../USER/inc -I ../Driver/CMSIS/GD/GD32F4xx/Include -I ../Components/bsp -I ../Components/oled -I ../Components/gd25qxx -I ../Components/ebtn -I ../Components/sdio -I ../Components/fatfs -I ../APP

-I./RTE/_McuSTUDIO_F470VET6

-IC:/Keil_v5/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-IC:/Keil_v5/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-IC:/Keil_v5/GorgonMeducer/perf_counter/2.3.3

-D__UVISION_VERSION="541" -DGD32F470 -D_RTE_ -DUSE_STDPERIPH_DRIVER -DGD32F470  -include "Pre_Include_Global.h"

-o ./output/gd32f4xx_misc.o -MMD)
I (RTE\_McuSTUDIO_F470VET6\Pre_Include_Global.h)(0x6846EBF3)
I (..\Libraries\Include\gd32f4xx_misc.h)(0x6846EBA7)
I (..\Driver\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x6846EBA6)
I (C:\Keil_v5\ARM\CMSIS\6.1.0\CMSIS\Core\Include\core_cm4.h)(0x664BD888)
I (..\Driver\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x6846EBA6)
I (..\USER\inc\gd32f4xx_libopt.h)(0x6846EBAD)
I (..\Libraries\Include\gd32f4xx_rcu.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_adc.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_can.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_crc.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_ctc.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_dac.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_dbg.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_dci.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_dma.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_exti.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_fmc.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_fwdgt.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_gpio.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_syscfg.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_i2c.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_iref.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_pmu.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_rtc.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_sdio.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_spi.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_timer.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_trng.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_usart.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_wwdgt.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_enet.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_exmc.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_ipa.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_tli.h)(0x6846EBA7)
F (..\Libraries\Source\gd32f4xx_pmu.c)(0x6846EBA8)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O1 -ffunction-sections -w -I ../Libraries/Include -I ../USER/inc -I ../Driver/CMSIS/GD/GD32F4xx/Include -I ../Components/bsp -I ../Components/oled -I ../Components/gd25qxx -I ../Components/ebtn -I ../Components/sdio -I ../Components/fatfs -I ../APP

-I./RTE/_McuSTUDIO_F470VET6

-IC:/Keil_v5/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-IC:/Keil_v5/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-IC:/Keil_v5/GorgonMeducer/perf_counter/2.3.3

-D__UVISION_VERSION="541" -DGD32F470 -D_RTE_ -DUSE_STDPERIPH_DRIVER -DGD32F470  -include "Pre_Include_Global.h"

-o ./output/gd32f4xx_pmu.o -MMD)
I (RTE\_McuSTUDIO_F470VET6\Pre_Include_Global.h)(0x6846EBF3)
I (..\Libraries\Include\gd32f4xx_pmu.h)(0x6846EBA7)
I (..\Driver\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x6846EBA6)
I (C:\Keil_v5\ARM\CMSIS\6.1.0\CMSIS\Core\Include\core_cm4.h)(0x664BD888)
I (..\Driver\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x6846EBA6)
I (..\USER\inc\gd32f4xx_libopt.h)(0x6846EBAD)
I (..\Libraries\Include\gd32f4xx_rcu.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_adc.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_can.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_crc.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_ctc.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_dac.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_dbg.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_dci.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_dma.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_exti.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_fmc.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_fwdgt.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_gpio.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_syscfg.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_i2c.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_iref.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_rtc.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_sdio.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_spi.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_timer.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_trng.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_usart.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_wwdgt.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_misc.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_enet.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_exmc.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_ipa.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_tli.h)(0x6846EBA7)
F (..\Libraries\Source\gd32f4xx_rcu.c)(0x6846EBA8)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O1 -ffunction-sections -w -I ../Libraries/Include -I ../USER/inc -I ../Driver/CMSIS/GD/GD32F4xx/Include -I ../Components/bsp -I ../Components/oled -I ../Components/gd25qxx -I ../Components/ebtn -I ../Components/sdio -I ../Components/fatfs -I ../APP

-I./RTE/_McuSTUDIO_F470VET6

-IC:/Keil_v5/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-IC:/Keil_v5/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-IC:/Keil_v5/GorgonMeducer/perf_counter/2.3.3

-D__UVISION_VERSION="541" -DGD32F470 -D_RTE_ -DUSE_STDPERIPH_DRIVER -DGD32F470  -include "Pre_Include_Global.h"

-o ./output/gd32f4xx_rcu.o -MMD)
I (RTE\_McuSTUDIO_F470VET6\Pre_Include_Global.h)(0x6846EBF3)
I (..\Libraries\Include\gd32f4xx_rcu.h)(0x6846EBA7)
I (..\Driver\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x6846EBA6)
I (C:\Keil_v5\ARM\CMSIS\6.1.0\CMSIS\Core\Include\core_cm4.h)(0x664BD888)
I (..\Driver\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x6846EBA6)
I (..\USER\inc\gd32f4xx_libopt.h)(0x6846EBAD)
I (..\Libraries\Include\gd32f4xx_adc.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_can.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_crc.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_ctc.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_dac.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_dbg.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_dci.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_dma.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_exti.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_fmc.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_fwdgt.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_gpio.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_syscfg.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_i2c.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_iref.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_pmu.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_rtc.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_sdio.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_spi.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_timer.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_trng.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_usart.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_wwdgt.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_misc.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_enet.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_exmc.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_ipa.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_tli.h)(0x6846EBA7)
F (..\Libraries\Source\gd32f4xx_rtc.c)(0x6846EBA8)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O1 -ffunction-sections -w -I ../Libraries/Include -I ../USER/inc -I ../Driver/CMSIS/GD/GD32F4xx/Include -I ../Components/bsp -I ../Components/oled -I ../Components/gd25qxx -I ../Components/ebtn -I ../Components/sdio -I ../Components/fatfs -I ../APP

-I./RTE/_McuSTUDIO_F470VET6

-IC:/Keil_v5/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-IC:/Keil_v5/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-IC:/Keil_v5/GorgonMeducer/perf_counter/2.3.3

-D__UVISION_VERSION="541" -DGD32F470 -D_RTE_ -DUSE_STDPERIPH_DRIVER -DGD32F470  -include "Pre_Include_Global.h"

-o ./output/gd32f4xx_rtc.o -MMD)
I (RTE\_McuSTUDIO_F470VET6\Pre_Include_Global.h)(0x6846EBF3)
I (..\Libraries\Include\gd32f4xx_rtc.h)(0x6846EBA7)
I (..\Driver\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x6846EBA6)
I (C:\Keil_v5\ARM\CMSIS\6.1.0\CMSIS\Core\Include\core_cm4.h)(0x664BD888)
I (..\Driver\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x6846EBA6)
I (..\USER\inc\gd32f4xx_libopt.h)(0x6846EBAD)
I (..\Libraries\Include\gd32f4xx_rcu.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_adc.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_can.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_crc.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_ctc.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_dac.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_dbg.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_dci.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_dma.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_exti.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_fmc.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_fwdgt.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_gpio.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_syscfg.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_i2c.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_iref.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_pmu.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_sdio.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_spi.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_timer.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_trng.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_usart.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_wwdgt.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_misc.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_enet.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_exmc.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_ipa.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_tli.h)(0x6846EBA7)
F (..\Libraries\Source\gd32f4xx_sdio.c)(0x6846EBA8)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O1 -ffunction-sections -w -I ../Libraries/Include -I ../USER/inc -I ../Driver/CMSIS/GD/GD32F4xx/Include -I ../Components/bsp -I ../Components/oled -I ../Components/gd25qxx -I ../Components/ebtn -I ../Components/sdio -I ../Components/fatfs -I ../APP

-I./RTE/_McuSTUDIO_F470VET6

-IC:/Keil_v5/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-IC:/Keil_v5/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-IC:/Keil_v5/GorgonMeducer/perf_counter/2.3.3

-D__UVISION_VERSION="541" -DGD32F470 -D_RTE_ -DUSE_STDPERIPH_DRIVER -DGD32F470  -include "Pre_Include_Global.h"

-o ./output/gd32f4xx_sdio.o -MMD)
I (RTE\_McuSTUDIO_F470VET6\Pre_Include_Global.h)(0x6846EBF3)
I (..\Libraries\Include\gd32f4xx_sdio.h)(0x6846EBA7)
I (..\Driver\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x6846EBA6)
I (C:\Keil_v5\ARM\CMSIS\6.1.0\CMSIS\Core\Include\core_cm4.h)(0x664BD888)
I (..\Driver\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x6846EBA6)
I (..\USER\inc\gd32f4xx_libopt.h)(0x6846EBAD)
I (..\Libraries\Include\gd32f4xx_rcu.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_adc.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_can.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_crc.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_ctc.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_dac.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_dbg.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_dci.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_dma.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_exti.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_fmc.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_fwdgt.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_gpio.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_syscfg.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_i2c.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_iref.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_pmu.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_rtc.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_spi.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_timer.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_trng.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_usart.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_wwdgt.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_misc.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_enet.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_exmc.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_ipa.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_tli.h)(0x6846EBA7)
F (..\Libraries\Source\gd32f4xx_spi.c)(0x6846EBA8)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O1 -ffunction-sections -w -I ../Libraries/Include -I ../USER/inc -I ../Driver/CMSIS/GD/GD32F4xx/Include -I ../Components/bsp -I ../Components/oled -I ../Components/gd25qxx -I ../Components/ebtn -I ../Components/sdio -I ../Components/fatfs -I ../APP

-I./RTE/_McuSTUDIO_F470VET6

-IC:/Keil_v5/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-IC:/Keil_v5/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-IC:/Keil_v5/GorgonMeducer/perf_counter/2.3.3

-D__UVISION_VERSION="541" -DGD32F470 -D_RTE_ -DUSE_STDPERIPH_DRIVER -DGD32F470  -include "Pre_Include_Global.h"

-o ./output/gd32f4xx_spi.o -MMD)
I (RTE\_McuSTUDIO_F470VET6\Pre_Include_Global.h)(0x6846EBF3)
I (..\Libraries\Include\gd32f4xx_spi.h)(0x6846EBA7)
I (..\Driver\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x6846EBA6)
I (C:\Keil_v5\ARM\CMSIS\6.1.0\CMSIS\Core\Include\core_cm4.h)(0x664BD888)
I (..\Driver\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x6846EBA6)
I (..\USER\inc\gd32f4xx_libopt.h)(0x6846EBAD)
I (..\Libraries\Include\gd32f4xx_rcu.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_adc.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_can.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_crc.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_ctc.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_dac.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_dbg.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_dci.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_dma.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_exti.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_fmc.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_fwdgt.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_gpio.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_syscfg.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_i2c.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_iref.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_pmu.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_rtc.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_sdio.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_timer.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_trng.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_usart.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_wwdgt.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_misc.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_enet.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_exmc.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_ipa.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_tli.h)(0x6846EBA7)
F (..\Libraries\Source\gd32f4xx_syscfg.c)(0x6846EBA8)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O1 -ffunction-sections -w -I ../Libraries/Include -I ../USER/inc -I ../Driver/CMSIS/GD/GD32F4xx/Include -I ../Components/bsp -I ../Components/oled -I ../Components/gd25qxx -I ../Components/ebtn -I ../Components/sdio -I ../Components/fatfs -I ../APP

-I./RTE/_McuSTUDIO_F470VET6

-IC:/Keil_v5/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-IC:/Keil_v5/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-IC:/Keil_v5/GorgonMeducer/perf_counter/2.3.3

-D__UVISION_VERSION="541" -DGD32F470 -D_RTE_ -DUSE_STDPERIPH_DRIVER -DGD32F470  -include "Pre_Include_Global.h"

-o ./output/gd32f4xx_syscfg.o -MMD)
I (RTE\_McuSTUDIO_F470VET6\Pre_Include_Global.h)(0x6846EBF3)
I (..\Libraries\Include\gd32f4xx_syscfg.h)(0x6846EBA7)
I (..\Driver\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x6846EBA6)
I (C:\Keil_v5\ARM\CMSIS\6.1.0\CMSIS\Core\Include\core_cm4.h)(0x664BD888)
I (..\Driver\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x6846EBA6)
I (..\USER\inc\gd32f4xx_libopt.h)(0x6846EBAD)
I (..\Libraries\Include\gd32f4xx_rcu.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_adc.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_can.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_crc.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_ctc.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_dac.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_dbg.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_dci.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_dma.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_exti.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_fmc.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_fwdgt.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_gpio.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_i2c.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_iref.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_pmu.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_rtc.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_sdio.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_spi.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_timer.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_trng.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_usart.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_wwdgt.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_misc.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_enet.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_exmc.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_ipa.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_tli.h)(0x6846EBA7)
F (..\Libraries\Source\gd32f4xx_timer.c)(0x6846EBA8)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O1 -ffunction-sections -w -I ../Libraries/Include -I ../USER/inc -I ../Driver/CMSIS/GD/GD32F4xx/Include -I ../Components/bsp -I ../Components/oled -I ../Components/gd25qxx -I ../Components/ebtn -I ../Components/sdio -I ../Components/fatfs -I ../APP

-I./RTE/_McuSTUDIO_F470VET6

-IC:/Keil_v5/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-IC:/Keil_v5/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-IC:/Keil_v5/GorgonMeducer/perf_counter/2.3.3

-D__UVISION_VERSION="541" -DGD32F470 -D_RTE_ -DUSE_STDPERIPH_DRIVER -DGD32F470  -include "Pre_Include_Global.h"

-o ./output/gd32f4xx_timer.o -MMD)
I (RTE\_McuSTUDIO_F470VET6\Pre_Include_Global.h)(0x6846EBF3)
I (..\Libraries\Include\gd32f4xx_timer.h)(0x6846EBA7)
I (..\Driver\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x6846EBA6)
I (C:\Keil_v5\ARM\CMSIS\6.1.0\CMSIS\Core\Include\core_cm4.h)(0x664BD888)
I (..\Driver\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x6846EBA6)
I (..\USER\inc\gd32f4xx_libopt.h)(0x6846EBAD)
I (..\Libraries\Include\gd32f4xx_rcu.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_adc.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_can.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_crc.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_ctc.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_dac.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_dbg.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_dci.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_dma.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_exti.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_fmc.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_fwdgt.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_gpio.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_syscfg.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_i2c.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_iref.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_pmu.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_rtc.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_sdio.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_spi.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_trng.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_usart.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_wwdgt.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_misc.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_enet.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_exmc.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_ipa.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_tli.h)(0x6846EBA7)
F (..\Libraries\Source\gd32f4xx_tli.c)(0x6846EBA8)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O1 -ffunction-sections -w -I ../Libraries/Include -I ../USER/inc -I ../Driver/CMSIS/GD/GD32F4xx/Include -I ../Components/bsp -I ../Components/oled -I ../Components/gd25qxx -I ../Components/ebtn -I ../Components/sdio -I ../Components/fatfs -I ../APP

-I./RTE/_McuSTUDIO_F470VET6

-IC:/Keil_v5/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-IC:/Keil_v5/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-IC:/Keil_v5/GorgonMeducer/perf_counter/2.3.3

-D__UVISION_VERSION="541" -DGD32F470 -D_RTE_ -DUSE_STDPERIPH_DRIVER -DGD32F470  -include "Pre_Include_Global.h"

-o ./output/gd32f4xx_tli.o -MMD)
I (RTE\_McuSTUDIO_F470VET6\Pre_Include_Global.h)(0x6846EBF3)
I (..\Libraries\Include\gd32f4xx_tli.h)(0x6846EBA7)
I (..\Driver\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x6846EBA6)
I (C:\Keil_v5\ARM\CMSIS\6.1.0\CMSIS\Core\Include\core_cm4.h)(0x664BD888)
I (..\Driver\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x6846EBA6)
I (..\USER\inc\gd32f4xx_libopt.h)(0x6846EBAD)
I (..\Libraries\Include\gd32f4xx_rcu.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_adc.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_can.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_crc.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_ctc.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_dac.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_dbg.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_dci.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_dma.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_exti.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_fmc.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_fwdgt.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_gpio.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_syscfg.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_i2c.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_iref.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_pmu.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_rtc.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_sdio.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_spi.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_timer.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_trng.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_usart.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_wwdgt.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_misc.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_enet.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_exmc.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_ipa.h)(0x6846EBA7)
F (..\Libraries\Source\gd32f4xx_trng.c)(0x6846EBA8)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O1 -ffunction-sections -w -I ../Libraries/Include -I ../USER/inc -I ../Driver/CMSIS/GD/GD32F4xx/Include -I ../Components/bsp -I ../Components/oled -I ../Components/gd25qxx -I ../Components/ebtn -I ../Components/sdio -I ../Components/fatfs -I ../APP

-I./RTE/_McuSTUDIO_F470VET6

-IC:/Keil_v5/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-IC:/Keil_v5/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-IC:/Keil_v5/GorgonMeducer/perf_counter/2.3.3

-D__UVISION_VERSION="541" -DGD32F470 -D_RTE_ -DUSE_STDPERIPH_DRIVER -DGD32F470  -include "Pre_Include_Global.h"

-o ./output/gd32f4xx_trng.o -MMD)
I (RTE\_McuSTUDIO_F470VET6\Pre_Include_Global.h)(0x6846EBF3)
I (..\Libraries\Include\gd32f4xx_trng.h)(0x6846EBA7)
I (..\Driver\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x6846EBA6)
I (C:\Keil_v5\ARM\CMSIS\6.1.0\CMSIS\Core\Include\core_cm4.h)(0x664BD888)
I (..\Driver\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x6846EBA6)
I (..\USER\inc\gd32f4xx_libopt.h)(0x6846EBAD)
I (..\Libraries\Include\gd32f4xx_rcu.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_adc.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_can.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_crc.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_ctc.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_dac.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_dbg.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_dci.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_dma.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_exti.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_fmc.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_fwdgt.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_gpio.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_syscfg.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_i2c.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_iref.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_pmu.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_rtc.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_sdio.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_spi.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_timer.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_usart.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_wwdgt.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_misc.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_enet.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_exmc.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_ipa.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_tli.h)(0x6846EBA7)
F (..\Libraries\Source\gd32f4xx_usart.c)(0x6846EBA8)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O1 -ffunction-sections -w -I ../Libraries/Include -I ../USER/inc -I ../Driver/CMSIS/GD/GD32F4xx/Include -I ../Components/bsp -I ../Components/oled -I ../Components/gd25qxx -I ../Components/ebtn -I ../Components/sdio -I ../Components/fatfs -I ../APP

-I./RTE/_McuSTUDIO_F470VET6

-IC:/Keil_v5/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-IC:/Keil_v5/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-IC:/Keil_v5/GorgonMeducer/perf_counter/2.3.3

-D__UVISION_VERSION="541" -DGD32F470 -D_RTE_ -DUSE_STDPERIPH_DRIVER -DGD32F470  -include "Pre_Include_Global.h"

-o ./output/gd32f4xx_usart.o -MMD)
I (RTE\_McuSTUDIO_F470VET6\Pre_Include_Global.h)(0x6846EBF3)
I (..\Libraries\Include\gd32f4xx_usart.h)(0x6846EBA7)
I (..\Driver\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x6846EBA6)
I (C:\Keil_v5\ARM\CMSIS\6.1.0\CMSIS\Core\Include\core_cm4.h)(0x664BD888)
I (..\Driver\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x6846EBA6)
I (..\USER\inc\gd32f4xx_libopt.h)(0x6846EBAD)
I (..\Libraries\Include\gd32f4xx_rcu.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_adc.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_can.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_crc.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_ctc.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_dac.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_dbg.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_dci.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_dma.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_exti.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_fmc.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_fwdgt.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_gpio.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_syscfg.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_i2c.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_iref.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_pmu.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_rtc.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_sdio.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_spi.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_timer.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_trng.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_wwdgt.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_misc.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_enet.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_exmc.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_ipa.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_tli.h)(0x6846EBA7)
F (..\Libraries\Source\gd32f4xx_wwdgt.c)(0x6846EBA8)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O1 -ffunction-sections -w -I ../Libraries/Include -I ../USER/inc -I ../Driver/CMSIS/GD/GD32F4xx/Include -I ../Components/bsp -I ../Components/oled -I ../Components/gd25qxx -I ../Components/ebtn -I ../Components/sdio -I ../Components/fatfs -I ../APP

-I./RTE/_McuSTUDIO_F470VET6

-IC:/Keil_v5/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-IC:/Keil_v5/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-IC:/Keil_v5/GorgonMeducer/perf_counter/2.3.3

-D__UVISION_VERSION="541" -DGD32F470 -D_RTE_ -DUSE_STDPERIPH_DRIVER -DGD32F470  -include "Pre_Include_Global.h"

-o ./output/gd32f4xx_wwdgt.o -MMD)
I (RTE\_McuSTUDIO_F470VET6\Pre_Include_Global.h)(0x6846EBF3)
I (..\Libraries\Include\gd32f4xx_wwdgt.h)(0x6846EBA7)
I (..\Driver\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x6846EBA6)
I (C:\Keil_v5\ARM\CMSIS\6.1.0\CMSIS\Core\Include\core_cm4.h)(0x664BD888)
I (..\Driver\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x6846EBA6)
I (..\USER\inc\gd32f4xx_libopt.h)(0x6846EBAD)
I (..\Libraries\Include\gd32f4xx_rcu.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_adc.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_can.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_crc.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_ctc.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_dac.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_dbg.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_dci.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_dma.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_exti.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_fmc.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_fwdgt.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_gpio.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_syscfg.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_i2c.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_iref.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_pmu.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_rtc.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_sdio.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_spi.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_timer.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_trng.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_usart.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_misc.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_enet.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_exmc.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_ipa.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_tli.h)(0x6846EBA7)
F (..\Libraries\startup_gd32f450_470.s)(0x6846EBA7)(--target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -masm=auto  -Wa,armasm,--diag_suppress=A1950W -c

-gdwarf-4

-I./RTE/_McuSTUDIO_F470VET6

-IC:/Keil_v5/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-IC:/Keil_v5/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-IC:/Keil_v5/GorgonMeducer/perf_counter/2.3.3

-Wa,armasm,--pd,"__UVISION_VERSION SETA 541" -Wa,armasm,--pd,"GD32F470 SETA 1" -Wa,armasm,--pd,"_RTE_ SETA 1"

-o ./output/startup_gd32f450_470.o)
F (..\Driver\CMSIS\GD\GD32F4xx\Source\system_gd32f4xx.c)(0x6846EBA6)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O1 -ffunction-sections -w -I ../Libraries/Include -I ../USER/inc -I ../Driver/CMSIS/GD/GD32F4xx/Include -I ../Components/bsp -I ../Components/oled -I ../Components/gd25qxx -I ../Components/ebtn -I ../Components/sdio -I ../Components/fatfs -I ../APP

-I./RTE/_McuSTUDIO_F470VET6

-IC:/Keil_v5/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-IC:/Keil_v5/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-IC:/Keil_v5/GorgonMeducer/perf_counter/2.3.3

-D__UVISION_VERSION="541" -DGD32F470 -D_RTE_ -DUSE_STDPERIPH_DRIVER -DGD32F470  -include "Pre_Include_Global.h"

-o ./output/system_gd32f4xx.o -MMD)
I (RTE\_McuSTUDIO_F470VET6\Pre_Include_Global.h)(0x6846EBF3)
I (..\Driver\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x6846EBA6)
I (C:\Keil_v5\ARM\CMSIS\6.1.0\CMSIS\Core\Include\core_cm4.h)(0x664BD888)
I (..\Driver\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x6846EBA6)
I (..\USER\inc\gd32f4xx_libopt.h)(0x6846EBAD)
I (..\Libraries\Include\gd32f4xx_rcu.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_adc.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_can.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_crc.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_ctc.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_dac.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_dbg.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_dci.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_dma.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_exti.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_fmc.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_fwdgt.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_gpio.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_syscfg.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_i2c.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_iref.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_pmu.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_rtc.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_sdio.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_spi.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_timer.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_trng.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_usart.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_wwdgt.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_misc.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_enet.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_exmc.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_ipa.h)(0x6846EBA7)
I (..\Libraries\Include\gd32f4xx_tli.h)(0x6846EBA7)
F (..\readme.txt)(0x00000000)()
F (..\io_multiplexing_table.txt)(0x6846EBA5)()
F (C:/Keil_v5/GorgonMeducer/perf_counter/2.3.3/perf_counter.c)(0x6842F7C4)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O1 -ffunction-sections -w -I ../Libraries/Include -I ../USER/inc -I ../Driver/CMSIS/GD/GD32F4xx/Include -I ../Components/bsp -I ../Components/oled -I ../Components/gd25qxx -I ../Components/ebtn -I ../Components/sdio -I ../Components/fatfs -I ../APP

-I./RTE/_McuSTUDIO_F470VET6

-IC:/Keil_v5/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-IC:/Keil_v5/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-IC:/Keil_v5/GorgonMeducer/perf_counter/2.3.3

-D__UVISION_VERSION="541" -DGD32F470 -D_RTE_ -DUSE_STDPERIPH_DRIVER -DGD32F470  -include "Pre_Include_Global.h"

-o ./output/perf_counter.o -MMD)
I (RTE\_McuSTUDIO_F470VET6\Pre_Include_Global.h)(0x6846EBF3)
I (C:\Keil_v5\ARM\CMSIS\6.1.0\CMSIS\Core\Include\cmsis_compiler.h)(0x664BD888)
I (C:\Keil_v5\ARM\CMSIS\6.1.0\CMSIS\Core\Include\cmsis_armclang.h)(0x664BD888)
I (C:\Keil_v5\GorgonMeducer\perf_counter\2.3.3\perf_counter.h)(0x6842F7C4)
I (C:\Keil_v5\GorgonMeducer\perf_counter\2.3.3\perfc_port_default.h)(0x6842F7C4)
F (C:/Keil_v5/GorgonMeducer/perf_counter/2.3.3/perfc_port_default.c)(0x6842F7C4)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O1 -ffunction-sections -w -I ../Libraries/Include -I ../USER/inc -I ../Driver/CMSIS/GD/GD32F4xx/Include -I ../Components/bsp -I ../Components/oled -I ../Components/gd25qxx -I ../Components/ebtn -I ../Components/sdio -I ../Components/fatfs -I ../APP

-I./RTE/_McuSTUDIO_F470VET6

-IC:/Keil_v5/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-IC:/Keil_v5/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-IC:/Keil_v5/GorgonMeducer/perf_counter/2.3.3

-D__UVISION_VERSION="541" -DGD32F470 -D_RTE_ -DUSE_STDPERIPH_DRIVER -DGD32F470  -include "Pre_Include_Global.h"

-o ./output/perfc_port_default.o -MMD)
I (RTE\_McuSTUDIO_F470VET6\Pre_Include_Global.h)(0x6846EBF3)
I (C:\Keil_v5\ARM\CMSIS\6.1.0\CMSIS\Core\Include\cmsis_compiler.h)(0x664BD888)
I (C:\Keil_v5\ARM\CMSIS\6.1.0\CMSIS\Core\Include\cmsis_armclang.h)(0x664BD888)
I (C:\Keil_v5\GorgonMeducer\perf_counter\2.3.3\perf_counter.h)(0x6842F7C4)
I (C:\Keil_v5\GorgonMeducer\perf_counter\2.3.3\perfc_port_default.h)(0x6842F7C4)
F (C:/Keil_v5/GorgonMeducer/perf_counter/2.3.3/systick_wrapper_gnu.s)(0x6842F7C4)(--target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -masm=auto  -Wa,armasm,--diag_suppress=A1950W -c

-gdwarf-4

-I./RTE/_McuSTUDIO_F470VET6

-IC:/Keil_v5/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-IC:/Keil_v5/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-IC:/Keil_v5/GorgonMeducer/perf_counter/2.3.3

-Wa,armasm,--pd,"__UVISION_VERSION SETA 541" -Wa,armasm,--pd,"GD32F470 SETA 1" -Wa,armasm,--pd,"_RTE_ SETA 1"

-o ./output/systick_wrapper_gnu.o)

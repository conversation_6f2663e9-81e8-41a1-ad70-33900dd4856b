#include "mcu_cmic_gd32f470vet6.h"

extern rtc_parameter_struct rtc_initpara;

/*!
    \brief      display the current time
    \param[in]  none
    \param[out] none
    \retval     none
*/
void rtc_task(void)
{
    // RTC time is now displayed in oled_app.c as part of the data logger interface
    // This function is kept for compatibility but no longer displays anything
    rtc_current_time_get(&rtc_initpara);
}
